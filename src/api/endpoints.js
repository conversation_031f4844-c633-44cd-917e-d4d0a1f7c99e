/**
 * TPA API Endpoint Wrappers
 * 
 * High-level wrapper functions for TPA API endpoints with parameter validation,
 * error handling, and response processing.
 */

import { tpaApiConfig } from '../config/env.js';
import { defaultClient } from './client.js';
import {
  validatePolicyListParams,
  validateClaimListParams,
  validatePolicyDetailParams,
  validateOrThrow
} from '../utils/validation.js';
import { logError } from './errors.js';

/**
 * Search and list insurance policies
 * @param {Object} params - Search parameters (must match one valid combination)
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Policy list response
 * 
 * @example
 * // Search by insurer code and citizen ID
 * const policies = await searchPolicies({
 *   INSURER_CODE: 'INS001',
 *   CITIZEN_ID: '1234567890123'
 * });
 * 
 * @example
 * // Search by insurer code and English name
 * const policies = await searchPolicies({
 *   INSURER_CODE: 'INS002',
 *   NAME_EN: 'Wichai Kengmak'
 * });
 */
export async function searchPolicies(params, options = {}) {
  try {
    const cleanParams = validateOrThrow(params, validatePolicyListParams);

    const response = await defaultClient.get(tpaApiConfig.endpointPolicyList, cleanParams, options);

    const data = response.data || response;
    return {
      success: true,
      data: data,
      total: response.total || (Array.isArray(data) ? data.length : (data ? 1 : 0)),
      params: cleanParams
    };

  } catch (error) {
    logError(error, { endpoint: tpaApiConfig.endpointPolicyList, params });
    throw error;
  }
}

/**
 * Get detailed policy information for a specific member
 * @param {string} memberCode - Member code (e.g., 'MEM001')
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Policy detail response
 * 
 * @example
 * // Get policy details for member
 * const policyDetail = await getPolicyDetail('MEM001');
 */
export async function getPolicyDetail(memberCode, options = {}) {
  try {
    const params = { MEMBER_CODE: memberCode };
    const cleanParams = validateOrThrow(params, validatePolicyDetailParams);

    const response = await defaultClient.get(tpaApiConfig.endpointPolicyDetail, cleanParams, options);

    return {
      success: true,
      data: response,
      memberCode: cleanParams.MEMBER_CODE
    };

  } catch (error) {
    logError(error, { endpoint: tpaApiConfig.endpointPolicyDetail, memberCode });
    throw error;
  }
}

/**
 * List claims history for a member
 * @param {Object} params - Search parameters (MEMBER_CODE or INSURER_CODE + CITIZEN_ID)
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Claims list response
 * 
 * @example
 * // Get claims by member code
 * const claims = await getClaimsList({ MEMBER_CODE: 'MEM001' });
 * 
 * @example
 * // Get claims by insurer code and citizen ID
 * const claims = await getClaimsList({
 *   INSURER_CODE: 'INS001',
 *   CITIZEN_ID: '1234567890123'
 * });
 */
export async function getClaimsList(params, options = {}) {
  try {
    const cleanParams = validateOrThrow(params, validateClaimListParams);

    const response = await defaultClient.get(tpaApiConfig.endpointClaimList, cleanParams, options);

    const data = response.data || response;
    return {
      success: true,
      data: data,
      total: response.total || (Array.isArray(data) ? data.length : (data ? 1 : 0)),
      params: cleanParams
    };

  } catch (error) {
    logError(error, { endpoint: tpaApiConfig.endpointClaimList, params });
    throw error;
  }
}

/**
 * Check API health status
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Health check response
 */
export async function checkHealth(options = {}) {
  try {
    const response = await defaultClient.healthCheck(options);

    return {
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError(error, { endpoint: tpaApiConfig.endpointHealth });
    throw error;
  }
}

/**
 * Get API root information
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Root endpoint response
 */
export async function getApiInfo(options = {}) {
  try {
    const response = await defaultClient.get('/', {}, options);

    return {
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError(error, { endpoint: 'root' });
    throw error;
  }
}

/**
 * Convenience function to search policies by citizen ID
 * @param {string} citizenId - Citizen ID
 * @param {string} insurerCode - Insurer code (defaults to 'INS001')
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Policy list response
 */
export async function searchPoliciesByCitizenId(citizenId, insurerCode = 'INS001', options = {}) {
  return searchPolicies({
    INSURER_CODE: insurerCode,
    CITIZEN_ID: citizenId
  }, options);
}

/**
 * Convenience function to search policies by policy number and name
 * @param {string} policyNo - Policy number
 * @param {string} name - Name (Thai or English)
 * @param {string} insurerCode - Insurer code (defaults to 'INS001')
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Policy list response
 */
export async function searchPoliciesByPolicyAndName(policyNo, name, insurerCode = 'INS001', options = {}) {
  // Detect if name is Thai or English (simple heuristic)
  const isThai = /[\u0E00-\u0E7F]/.test(name);

  const params = {
    INSURER_CODE: insurerCode,
    POLICY_NO: policyNo
  };

  if (isThai) {
    params.NAME_TH = name;
  } else {
    params.NAME_EN = name;
  }

  return searchPolicies(params, options);
}

/**
 * Convenience function to search policies by name only
 * @param {string} name - Name (Thai or English)
 * @param {string} insurerCode - Insurer code (defaults to 'INS001')
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Policy list response
 */
export async function searchPoliciesByName(name, insurerCode = 'INS001', options = {}) {
  // Detect if name is Thai or English (simple heuristic)
  const isThai = /[\u0E00-\u0E7F]/.test(name);

  const params = {
    INSURER_CODE: insurerCode
  };

  if (isThai) {
    params.NAME_TH = name;
  } else {
    params.NAME_EN = name;
  }

  return searchPolicies(params, options);
}

/**
 * Convenience function to get claims by citizen ID
 * @param {string} citizenId - Citizen ID
 * @param {string} insurerCode - Insurer code (defaults to 'INS001')
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Claims list response
 */
export async function getClaimsByCitizenId(citizenId, insurerCode = 'INS001', options = {}) {
  return getClaimsList({
    INSURER_CODE: insurerCode,
    CITIZEN_ID: citizenId
  }, options);
}

/**
 * Convenience function to get claims by member code
 * @param {string} memberCode - Member code
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Claims list response
 */
export async function getClaimsByMemberCode(memberCode, options = {}) {
  return getClaimsList({
    MEMBER_CODE: memberCode
  }, options);
}

/**
 * Batch function to get both policy details and claims for a member
 * @param {string} memberCode - Member code
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Combined policy and claims data
 */
export async function getMemberData(memberCode, options = {}) {
  try {
    const [policyDetail, claimsList] = await Promise.all([
      getPolicyDetail(memberCode, options),
      getClaimsByMemberCode(memberCode, options)
    ]);

    return {
      success: true,
      memberCode,
      policy: policyDetail.data,
      claims: claimsList.data,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError(error, { endpoint: 'getMemberData', memberCode });
    throw error;
  }
}

/**
 * Get all available members (for member selection)
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} API response with member list
 */
export async function getAllMembersData(options = {}) {
  try {
    // In a real implementation, this would call an API endpoint
    // For now, we'll simulate the response structure
    const response = await defaultClient.get('/members', {}, options);

    return {
      success: true,
      data: response,
      message: 'Member list retrieved successfully',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logError(error, { endpoint: 'getAllMembersData' });
    throw error;
  }
}

/**
 * Get member policies by member code (specific to member context)
 * @param {string} memberCode - Member code to get policies for
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} API response with member policies
 */
export async function getMemberPolicies(memberCode, options = {}) {
  try {
    const params = { MEMBER_CODE: memberCode };
    const response = await defaultClient.get('/member/policies', params, options);

    return {
      success: true,
      data: response,
      memberCode,
      message: 'Member policies retrieved successfully',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logError(error, { endpoint: 'getMemberPolicies', memberCode });
    throw error;
  }
}

/**
 * Get member claims by member code (specific to member context)
 * @param {string} memberCode - Member code to get claims for
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} API response with member claims
 */
export async function getMemberClaims(memberCode, options = {}) {
  try {
    const params = { MEMBER_CODE: memberCode };
    const response = await defaultClient.get('/member/claims', params, options);

    return {
      success: true,
      data: response,
      memberCode,
      message: 'Member claims retrieved successfully',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logError(error, { endpoint: 'getMemberClaims', memberCode });
    throw error;
  }
}
