/**
 * TPA API HTTP Client
 * 
 * Core HTTP client for TPA API integration with retry logic, error handling,
 * and environment-based configuration.
 */

import { tpaApiConfig, getApiBaseUrl } from '../config/env.js';
import { ApiError, NetworkError, TimeoutError, ValidationError } from './errors.js';

/**
 * HTTP Client class for TPA API communication
 */
export class TpaApiClient {
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || getApiBaseUrl();
    this.timeout = config.timeout || tpaApiConfig.timeout;
    this.retryAttempts = config.retryAttempts || tpaApiConfig.retryAttempts;
    this.retryDelay = config.retryDelay || 1000; // Base delay in ms

    // Request interceptors
    this.requestInterceptors = [];
    this.responseInterceptors = [];
  }

  /**
   * Add request interceptor
   * @param {Function} interceptor - Function to modify request before sending
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   * @param {Function} interceptor - Function to modify response after receiving
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Apply request interceptors
   * @param {Object} config - Request configuration
   * @returns {Object} Modified request configuration
   */
  async applyRequestInterceptors(config) {
    let modifiedConfig = { ...config };

    for (const interceptor of this.requestInterceptors) {
      modifiedConfig = await interceptor(modifiedConfig);
    }

    return modifiedConfig;
  }

  /**
   * Apply response interceptors
   * @param {Response} response - Fetch response object
   * @returns {Response} Modified response
   */
  async applyResponseInterceptors(response) {
    let modifiedResponse = response;

    for (const interceptor of this.responseInterceptors) {
      modifiedResponse = await interceptor(modifiedResponse);
    }

    return modifiedResponse;
  }

  /**
   * Calculate retry delay with exponential backoff
   * @param {number} attempt - Current attempt number (0-based)
   * @returns {number} Delay in milliseconds
   */
  calculateRetryDelay(attempt) {
    return this.retryDelay * Math.pow(2, attempt) + Math.random() * 1000;
  }

  /**
   * Check if error is retryable
   * @param {Error} error - Error to check
   * @returns {boolean} Whether the error is retryable
   */
  isRetryableError(error) {
    if (error instanceof NetworkError) return true;
    if (error instanceof TimeoutError) return true;
    if (error instanceof ApiError) {
      // Retry on server errors (5xx) but not client errors (4xx)
      return error.status >= 500;
    }
    return false;
  }

  /**
   * Create AbortController with timeout
   * @returns {Object} Object with controller and timeoutId
   */
  createTimeoutController() {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, this.timeout);

    return { controller, timeoutId };
  }

  /**
   * Make HTTP request with retry logic
   * @param {string} endpoint - API endpoint path
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    let lastError;

    // Apply request interceptors
    const config = await this.applyRequestInterceptors({
      url,
      ...options
    });

    for (let attempt = 0; attempt <= this.retryAttempts; attempt++) {
      const { controller, timeoutId } = this.createTimeoutController();

      try {
        const requestOptions = {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...config.headers
          },
          signal: controller.signal,
          ...config
        };

        // Remove url from requestOptions as it's passed separately to fetch
        delete requestOptions.url;

        const response = await fetch(config.url, requestOptions);
        clearTimeout(timeoutId);

        // Apply response interceptors
        const interceptedResponse = await this.applyResponseInterceptors(response);

        if (!interceptedResponse.ok) {
          const errorData = await this.parseErrorResponse(interceptedResponse);
          throw new ApiError(
            errorData.message || `HTTP ${interceptedResponse.status}`,
            interceptedResponse.status,
            errorData
          );
        }

        const data = await interceptedResponse.json();
        return data;

      } catch (error) {
        clearTimeout(timeoutId);
        lastError = this.handleRequestError(error);

        // Don't retry if it's the last attempt or error is not retryable
        if (attempt === this.retryAttempts || !this.isRetryableError(lastError)) {
          throw lastError;
        }

        // Wait before retrying
        const delay = this.calculateRetryDelay(attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Parse error response from API
   * @param {Response} response - Fetch response object
   * @returns {Object} Parsed error data
   */
  async parseErrorResponse(response) {
    try {
      return await response.json();
    } catch {
      return {
        error: 'Unknown Error',
        message: `HTTP ${response.status} ${response.statusText}`,
        details: {}
      };
    }
  }

  /**
   * Handle request errors and convert to appropriate error types
   * @param {Error} error - Original error
   * @returns {Error} Converted error
   */
  handleRequestError(error) {
    if (error.name === 'AbortError' || error instanceof DOMException && error.name === 'AbortError') {
      return new TimeoutError('Request timeout');
    }

    if (error instanceof TypeError && error.message.includes('fetch')) {
      return new NetworkError('Network error - unable to connect to API');
    }

    if (error instanceof ApiError) {
      return error;
    }

    return new NetworkError(error.message || 'Unknown network error');
  }

  /**
   * Make GET request
   * @param {string} endpoint - API endpoint path
   * @param {Object} params - Query parameters
   * @param {Object} options - Additional request options
   * @returns {Promise<Object>} Response data
   */
  async get(endpoint, params = {}, options = {}) {
    const queryString = this.buildQueryString(params);
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;

    return this.request(url, {
      method: 'GET',
      ...options
    });
  }

  /**
   * Build query string from parameters
   * @param {Object} params - Parameters object
   * @returns {string} Query string
   */
  buildQueryString(params) {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    return searchParams.toString();
  }

  /**
   * Health check endpoint
   * @returns {Promise<Object>} Health status
   */
  async healthCheck() {
    return this.request(tpaApiConfig.endpointHealth);
  }
}

// Create default client instance
export const defaultClient = new TpaApiClient();

// Export convenience methods
export const get = (endpoint, params, options) => defaultClient.get(endpoint, params, options);
export const healthCheck = () => defaultClient.healthCheck();
