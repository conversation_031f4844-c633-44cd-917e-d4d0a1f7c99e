/**
 * Member Data Utilities
 *
 * Utilities for parsing and managing member data from CSV source.
 * This module handles the conversion of CSV data to structured member objects
 * and provides helper functions for member data manipulation.
 */

import { TypeCheckers } from '../api/types.js';
import { loadMemberData } from './csvReader.js';

/**
 * Cache for member data loaded from CSV
 */
let memberDataCache = null;
let memberDataPromise = null;

/**
 * Load member data from CSV with caching
 * @param {boolean} forceReload - Whether to force reload from CSV
 * @returns {Promise<Array>} Promise resolving to array of member objects
 */
async function loadMembers(forceReload = false) {
  if (forceReload || !memberDataCache) {
    if (!memberDataPromise || forceReload) {
      memberDataPromise = loadMemberData(forceReload);
    }
    memberDataCache = await memberDataPromise;
  }
  return memberDataCache;
}

/**
 * Get API citizen ID from citizen code
 * @param {string} citizenCode - Internal citizen code (e.g., '1234567890124')
 * @returns {string} API citizen ID (e.g., '1234567890124')
 */
export function getApiCitizenId(citizenCode) {
  return citizenCode;
}

/**
 * Enhance member data with display information for backward compatibility
 * Uses data from CSV and adds default values for missing fields
 * @param {Object} member - Member object from CSV with all fields
 * @returns {Object} Enhanced member object with backward compatibility
 */
function enhanceMemberData(member) {
  // CSV data already contains most fields, just add computed and default fields
  return {
    ...member,
    // Ensure required fields have defaults if missing
    memberStatus: member.memberStatus || 'Active',
    memberType: member.memberType || 'Principal',
    principleMemberCode: member.memberCode,
    principleName: `${member.nameTH} ${member.surnameTH}`,
    vip: member.vip || 'N',
    vipRemarks: member.vipRemarks || '',
    cardType: member.cardType || 'Standard',
    language: member.language || 'TH',
    citizenship: member.citizenship || 'Thai',
    countryCode: member.countryCode || 'TH',
    // Fields that will be fetched from API when needed
    birthDate: member.birthDate || null,
    gender: member.gender || null,
    mobile: member.mobile || null,
    email: member.email || null
  };
}

/**
 * Get all available members with enhanced data for backward compatibility
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Promise<Array>} Promise resolving to array of member objects with enhanced display data
 */
export async function getAllMembers(includeInactive = false) {
  try {
    const rawMembers = await loadMembers();

    // Filter by status if needed
    const filteredMembers = includeInactive
      ? rawMembers
      : rawMembers.filter(member => member.memberStatus !== 'Inactive');

    // Enhanced with display data for backward compatibility
    return filteredMembers.map(member => enhanceMemberData(member));
  } catch (error) {
    console.error('Error loading members:', error);
    // Return empty array if loading fails
    return [];
  }
}

/**
 * Get member by member code with enhanced data
 * @param {string} memberCode - Member code to search for
 * @returns {Promise<Object|null>} Promise resolving to enhanced member object or null if not found
 */
export async function getMemberByCode(memberCode) {
  try {
    const rawMembers = await loadMembers();
    const member = rawMembers.find(member => member.memberCode === memberCode);
    return member ? enhanceMemberData(member) : null;
  } catch (error) {
    console.error('Error loading member by code:', error);
    return null;
  }
}

/**
 * Get member display name - always returns Thai name for consistency
 * @param {Object} member - Member object
 * @param {string} language - Language preference (kept for compatibility, but always returns Thai)
 * @returns {string} Formatted display name in Thai
 */
export function getMemberDisplayName(member, language = 'TH') {
  if (!member) return '';

  // Always use Thai names for consistent interface
  // Note: language parameter kept for API compatibility but not used
  if (member.titleTH && member.nameTH && member.surnameTH) {
    return `${member.titleTH}${member.nameTH} ${member.surnameTH}`;
  }

  // Fallback to English if Thai is not available (rare case)
  if (member.titleEN && member.nameEN && member.surnameEN) {
    return `${member.titleEN} ${member.nameEN} ${member.surnameEN}`;
  }

  // Final fallback to member code if names are not available
  return member.memberCode || 'Unknown Member';
}

/**
 * Get member short name (first name only) - always returns Thai name for consistency
 * @param {Object} member - Member object
 * @param {string} language - Language preference (kept for compatibility, but always returns Thai)
 * @returns {string} Short name in Thai
 */
export function getMemberShortName(member, language = 'TH') {
  if (!member) return '';

  // Always use Thai names for consistent interface
  // Note: language parameter kept for API compatibility but not used
  if (member.nameTH) {
    return member.nameTH;
  }

  // Fallback to English if Thai is not available (rare case)
  if (member.nameEN) {
    return member.nameEN;
  }

  return member.memberCode || 'Unknown';
}

/**
 * Get members for dropdown display - always uses Thai names for consistency
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Promise<Array>} Promise resolving to array of member options for dropdown
 */
export async function getMemberOptions(includeInactive = false) {
  try {
    const members = await getAllMembers(includeInactive);

    return members.map(member => ({
      value: member.memberCode,
      label: getMemberDisplayName(member, 'TH'), // Always use Thai for consistency
      shortLabel: getMemberShortName(member, 'TH'), // Always use Thai for consistency
      status: member.memberStatus,
      vip: member.vip === 'Y',
      cardType: member.cardType,
      memberType: member.memberType,
      language: member.language,
      member: member // Include full member object for reference
    }));
  } catch (error) {
    console.error('Error loading member options:', error);
    return [];
  }
}

/**
 * Filter members by search term - prioritizes Thai names for consistency
 * @param {string} searchTerm - Search term to filter by
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Promise<Array>} Promise resolving to filtered array of enhanced members
 */
export async function searchMembers(searchTerm, includeInactive = false) {
  if (!searchTerm || searchTerm.trim() === '') {
    return await getAllMembers(includeInactive);
  }

  try {
    const term = searchTerm.toLowerCase().trim();
    const members = await getAllMembers(includeInactive);

    return members.filter(member => {
      // Search in essential fields and enhanced display data
      const searchFields = [
        member.memberCode,
        member.citizenID,
        member.insurerCode,
        member.titleTH,
        member.nameTH,
        member.surnameTH,
        member.nameEN, // Keep English as fallback for search
        member.surnameEN,
        member.email,
        member.mobile
      ];

      return searchFields.some(field =>
        field && field.toString().toLowerCase().includes(term)
      );
    });
  } catch (error) {
    console.error('Error searching members:', error);
    return [];
  }
}

/**
 * Validate member data structure
 * @param {Object} member - Member object to validate
 * @returns {Object} Validation result
 */
export function validateMemberData(member) {
  return TypeCheckers.validateMember(member);
}

/**
 * Get default member (first active member)
 * @returns {Promise<Object|null>} Promise resolving to default member object
 */
export async function getDefaultMember() {
  try {
    const activeMembers = await getAllMembers(false);
    return activeMembers.length > 0 ? activeMembers[0] : null;
  } catch (error) {
    console.error('Error loading default member:', error);
    return null;
  }
}

/**
 * Check if member is VIP
 * @param {Object} member - Member object
 * @returns {boolean} Whether member is VIP
 */
export function isMemberVip(member) {
  return member && member.vip === 'Y';
}

/**
 * Get member's card type display
 * @param {Object} member - Member object
 * @returns {string} Card type with emoji
 */
export function getMemberCardTypeDisplay(member) {
  if (!member || !member.cardType) return '';

  const cardTypeEmojis = {
    'Standard': '🟢',
    'Gold': '🟡',
    'Platinum': '⚪',
    'Diamond': '💎'
  };

  const emoji = cardTypeEmojis[member.cardType] || '';
  return `${emoji} ${member.cardType}`;
}
