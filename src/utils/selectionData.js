/**
 * Selection Data Utilities
 *
 * Utilities for managing citizen and insurer selection data for the two-step
 * member selection process. Provides functions to extract unique citizens
 * and their associated insurers from member data.
 */

import { getAllMembers } from './memberData.js';
import { loadInsurerData } from './csvReader.js';

/**
 * Cache for insurer data loaded from CSV
 */
let insurerDataCache = null;
let insurerDataPromise = null;

/**
 * Load insurer data from CSV with caching
 * @param {boolean} forceReload - Whether to force reload from CSV
 * @returns {Promise<Array>} Promise resolving to array of insurer objects
 */
async function loadInsurers(forceReload = false) {
  if (forceReload || !insurerDataCache) {
    if (!insurerDataPromise || forceReload) {
      insurerDataPromise = loadInsurerData(forceReload);
    }
    insurerDataCache = await insurerDataPromise;
  }
  return insurerDataCache;
}

/**
 * Get display name for insurer code
 * @param {string} insurerCode - Insurer code (e.g., 'INS001')
 * @returns {Promise<string>} Promise resolving to display name for insurer
 */
export async function getInsurerDisplayName(insurerCode) {
  try {
    const insurers = await loadInsurers();
    const insurer = insurers.find(ins => ins.insurerCode === insurerCode);
    return insurer ? `${insurer.displayNameTH} (${insurer.displayNameEN})` : insurerCode || 'Unknown Insurer';
  } catch (error) {
    console.error('Error loading insurer display name:', error);
    return insurerCode || 'Unknown Insurer';
  }
}

/**
 * Get display name for citizen ID
 * @param {string} citizenID - Citizen ID (e.g., '1234567890123')
 * @param {string} language - Language preference ('TH' or 'EN')
 * @returns {Promise<string>} Promise resolving to display name for citizen
 */
export async function getCitizenDisplayName(citizenID, language = 'TH') {
  try {
    const members = await getAllMembers();
    const member = members.find(m => m.citizenID === citizenID);
    if (!member) return citizenID || 'Unknown Citizen';

    if (language === 'EN') {
      return `${member.titleEN} ${member.nameEN} ${member.surnameEN}`;
    }
    return `${member.titleTH} ${member.nameTH} ${member.surnameTH}`;
  } catch (error) {
    console.error('Error loading citizen display name:', error);
    return citizenID || 'Unknown Citizen';
  }
}

/**
 * Get all unique citizens from member data
 * @returns {Promise<Array>} Promise resolving to array of citizen objects with display information
 */
export async function getAllCitizens() {
  try {
    const members = await getAllMembers();
    const citizenMap = new Map();

    // Extract unique citizens
    members.forEach(member => {
      if (!citizenMap.has(member.citizenID)) {
        citizenMap.set(member.citizenID, {
          citizenID: member.citizenID,
          displayName: `${member.titleTH} ${member.nameTH} ${member.surnameTH}`,
          displayNameEN: `${member.titleEN} ${member.nameEN} ${member.surnameEN}`,
          titleTH: member.titleTH,
          nameTH: member.nameTH,
          surnameTH: member.surnameTH,
          titleEN: member.titleEN,
          nameEN: member.nameEN,
          surnameEN: member.surnameEN
        });
      }
    });

    return Array.from(citizenMap.values()).sort((a, b) =>
      a.displayName.localeCompare(b.displayName, 'th')
    );
  } catch (error) {
    console.error('Error loading citizens:', error);
    return [];
  }
}

/**
 * Get insurers available for a specific citizen
 * @param {string} citizenID - Citizen ID to get insurers for
 * @returns {Promise<Array>} Promise resolving to array of insurer objects for the citizen
 */
export async function getInsurersForCitizen(citizenID) {
  if (!citizenID) return [];

  try {
    const members = await getAllMembers();
    const insurerMap = new Map();

    // Find all insurers for this citizen
    const citizenMembers = members.filter(member => member.citizenID === citizenID);

    for (const member of citizenMembers) {
      if (!insurerMap.has(member.insurerCode)) {
        const displayName = await getInsurerDisplayName(member.insurerCode);
        insurerMap.set(member.insurerCode, {
          insurerCode: member.insurerCode,
          displayName: displayName,
          memberCode: member.memberCode // Store associated member code
        });
      }
    }

    return Array.from(insurerMap.values()).sort((a, b) =>
      a.displayName.localeCompare(b.displayName, 'th')
    );
  } catch (error) {
    console.error('Error loading insurers for citizen:', error);
    return [];
  }
}

/**
 * Get all available insurers
 * @returns {Promise<Array>} Promise resolving to array of all insurer objects
 */
export async function getAllInsurers() {
  try {
    const members = await getAllMembers();
    const insurerMap = new Map();

    for (const member of members) {
      if (!insurerMap.has(member.insurerCode)) {
        const displayName = await getInsurerDisplayName(member.insurerCode);
        insurerMap.set(member.insurerCode, {
          insurerCode: member.insurerCode,
          displayName: displayName
        });
      }
    }

    return Array.from(insurerMap.values()).sort((a, b) =>
      a.displayName.localeCompare(b.displayName, 'th')
    );
  } catch (error) {
    console.error('Error loading all insurers:', error);
    return [];
  }
}

/**
 * Find member by citizen ID and insurer code
 * @param {string} citizenID - Citizen ID
 * @param {string} insurerCode - Insurer code
 * @returns {Promise<Object|null>} Promise resolving to member object or null if not found
 */
export async function findMemberByCitizenAndInsurer(citizenID, insurerCode) {
  if (!citizenID || !insurerCode) return null;

  try {
    const members = await getAllMembers();
    return members.find(member =>
      member.citizenID === citizenID && member.insurerCode === insurerCode
    ) || null;
  } catch (error) {
    console.error('Error finding member by citizen and insurer:', error);
    return null;
  }
}

/**
 * Validate citizen and insurer combination
 * @param {string} citizenID - Citizen ID to validate
 * @param {string} insurerCode - Insurer code to validate
 * @returns {Promise<Object>} Promise resolving to validation result with success flag and message
 */
export async function validateCitizenInsurerCombination(citizenID, insurerCode) {
  if (!citizenID) {
    return { success: false, message: 'กรุณาเลือกสมาชิก' };
  }

  if (!insurerCode) {
    return { success: false, message: 'กรุณาเลือกบริษัทประกัน' };
  }

  try {
    const member = await findMemberByCitizenAndInsurer(citizenID, insurerCode);
    if (!member) {
      return {
        success: false,
        message: 'ไม่พบข้อมูลสมาชิกสำหรับการเลือกนี้'
      };
    }

    return {
      success: true,
      message: 'การเลือกถูกต้อง',
      member
    };
  } catch (error) {
    console.error('Error validating citizen insurer combination:', error);
    return {
      success: false,
      message: 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล'
    };
  }
}

/**
 * Get citizen options for dropdown display
 * @returns {Promise<Array>} Promise resolving to array of citizen options formatted for dropdown
 */
export async function getCitizenOptions() {
  try {
    const citizens = await getAllCitizens();
    return citizens.map(citizen => ({
      value: citizen.citizenID,
      label: citizen.displayName,
      labelEN: citizen.displayNameEN,
      citizen: citizen
    }));
  } catch (error) {
    console.error('Error loading citizen options:', error);
    return [];
  }
}

/**
 * Get insurer options for dropdown display
 * @param {string} citizenID - Citizen ID to get insurers for (optional)
 * @returns {Promise<Array>} Promise resolving to array of insurer options formatted for dropdown
 */
export async function getInsurerOptions(citizenID = null) {
  try {
    const insurers = citizenID ? await getInsurersForCitizen(citizenID) : await getAllInsurers();
    return insurers.map(insurer => ({
      value: insurer.insurerCode,
      label: insurer.displayName,
      insurer: insurer
    }));
  } catch (error) {
    console.error('Error loading insurer options:', error);
    return [];
  }
}
