/**
 * Environment Configuration
 * 
 * This module provides access to environment variables for the TPA API integration.
 * It uses Vite's built-in environment variable handling.
 * 
 * Note: In Vite, only variables prefixed with VITE_ are exposed to the client.
 * Server-side variables (like API_*) need to be handled differently.
 */

// Client-side environment variables (prefixed with VITE_)
// Handle cases where import.meta.env might be undefined (e.g., in Node.js or SSR)
let env = {};
try {
  env = import.meta.env || {};
} catch (error) {
  // import.meta.env is not available (e.g., in Node.js)
  env = {};
}

export const clientConfig = {
  devMode: env.VITE_DEV_MODE === 'true',
  apiEnvironment: env.VITE_API_ENVIRONMENT || 'development',
  mode: env.MODE || 'development',
  isDev: env.DEV !== false,
  isProd: env.PROD === true
};

// TPA API Configuration
// These will be loaded from environment variables during build time
// For now, we'll use default values that match our .env file
export const tpaApiConfig = {
  baseUrl: env.VITE_API_BASE_URL,
  prefix: env.VITE_API_PREFIX,
  endpointHealth: env.VITE_API_HEALTH_ENDPOINT,
  endpointPolicyList: env.VITE_API_POLICY_LIST_ENDPOINT,
  endpointPolicyDetail: env.VITE_API_POLICY_DETAIL_ENDPOINT,
  endpointClaimList: env.VITE_API_CLAIM_LIST_ENDPOINT,
  timeout: parseInt(env.VITE_API_TIMEOUT),
  retryAttempts: parseInt(env.VITE_API_RETRY_ATTEMPTS)
};

// Full API base URL
export const getApiBaseUrl = () => {
  return `${tpaApiConfig.baseUrl}${tpaApiConfig.prefix}`;
};

// Environment validation
export const validateEnvironment = () => {
  const errors = [];

  if (!tpaApiConfig.baseUrl) {
    errors.push('API_BASE_URL is required');
  }

  if (!tpaApiConfig.prefix) {
    errors.push('API_PREFIX is required');
  }

  if (isNaN(tpaApiConfig.timeout) || tpaApiConfig.timeout <= 0) {
    errors.push('API_TIMEOUT must be a positive number');
  }

  if (isNaN(tpaApiConfig.retryAttempts) || tpaApiConfig.retryAttempts < 0) {
    errors.push('API_RETRY_ATTEMPTS must be a non-negative number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Log configuration in development mode
if (clientConfig.devMode && clientConfig.isDev) {
  console.log('Environment Configuration:', {
    client: clientConfig,
    tpaApi: tpaApiConfig,
    fullApiUrl: getApiBaseUrl()
  });

  const validation = validateEnvironment();
  if (!validation.isValid) {
    console.warn('Environment validation errors:', validation.errors);
  }
}
