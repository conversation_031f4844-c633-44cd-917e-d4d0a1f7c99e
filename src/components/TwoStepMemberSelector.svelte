<!--
  TwoStepMemberSelector Component

  A two-step member selection component with the following features:
  - First dropdown: Citizen selection with name and ID display
  - Second dropdown: Insurer selection (populated based on citizen selection)
  - Accessible design with ARIA labels and keyboard navigation
  - Session persistence across page navigation
  - Responsive design with Tailwind CSS
  - Thai language labels with English code/comments
  - Horizontal layout option for header placement
-->

<script>
  import { onMount } from "svelte";
  import {
    selectedCitizenStore,
    selectedInsurerStore,
    selectedMemberStore,
    selectCitizen,
    selectInsurer,
  } from "../stores/memberStore.js";
  import {
    getCitizenOptions,
    getInsurerOptions,
  } from "../utils/selectionData.js";

  // Component props
  export let compact = false;
  export let showLabels = true;
  export let horizontal = false;

  // Component state
  let citizenDropdownOpen = false;
  let insurerDropdownOpen = false;
  let citizenDropdownElement = null;
  let insurerDropdownElement = null;

  // Reactive data
  $: selectedCitizen = $selectedCitizenStore;
  $: selectedInsurer = $selectedInsurerStore;
  $: selectedMember = $selectedMemberStore;

  // Get options
  let citizenOptions = [];
  let insurerOptions = [];
  let loadingCitizens = false;
  let loadingInsurers = false;

  // Load citizen options on mount
  onMount(async () => {
    loadingCitizens = true;
    try {
      citizenOptions = await getCitizenOptions();
    } catch (error) {
      console.error("Error loading citizen options:", error);
      citizenOptions = [];
    } finally {
      loadingCitizens = false;
    }
  });

  // Update insurer options when citizen changes
  $: if (selectedCitizen) {
    loadInsurerOptions(selectedCitizen.citizenID);
  } else {
    insurerOptions = [];
  }

  // Async function to load insurer options
  async function loadInsurerOptions(citizenID) {
    loadingInsurers = true;
    try {
      insurerOptions = await getInsurerOptions(citizenID);
    } catch (error) {
      console.error("Error loading insurer options:", error);
      insurerOptions = [];
    } finally {
      loadingInsurers = false;
    }
  }

  // Handle citizen selection
  function handleCitizenSelect(option) {
    selectCitizen(option.value, option.citizen);
    closeCitizenDropdown();
  }

  // Handle insurer selection
  function handleInsurerSelect(option) {
    selectInsurer(option.value, option.insurer);
    closeInsurerDropdown();
  }

  // Dropdown control functions
  function openCitizenDropdown() {
    citizenDropdownOpen = true;
    insurerDropdownOpen = false;
  }

  function closeCitizenDropdown() {
    citizenDropdownOpen = false;
  }

  function openInsurerDropdown() {
    if (!selectedCitizen) return;
    insurerDropdownOpen = true;
    citizenDropdownOpen = false;
  }

  function closeInsurerDropdown() {
    insurerDropdownOpen = false;
  }

  // Handle click outside to close dropdowns
  function handleClickOutside(event) {
    if (
      citizenDropdownElement &&
      !citizenDropdownElement.contains(event.target)
    ) {
      closeCitizenDropdown();
    }
    if (
      insurerDropdownElement &&
      !insurerDropdownElement.contains(event.target)
    ) {
      closeInsurerDropdown();
    }
  }

  // Initialize component
  onMount(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<!-- Two-Step Member Selector -->
<div
  class="{horizontal
    ? 'flex flex-col sm:flex-row sm:space-x-2 sm:space-y-0 space-y-3'
    : 'flex flex-col space-y-3'}
    {compact ? 'min-w-[200px]' : 'min-w-[280px]'}"
>
  <!-- Citizen Selection (First Step) -->
  <div class="relative {horizontal ? 'flex-1 min-w-0' : ''}">
    {#if showLabels}
      <label
        for="citizen-selector"
        class="block text-sm font-medium text-gray-700 mb-1"
      >
        เลือกสมาชิก
      </label>
    {/if}

    <div
      bind:this={citizenDropdownElement}
      class="relative"
      role="combobox"
      aria-expanded={citizenDropdownOpen}
      aria-haspopup="listbox"
      aria-controls="citizen-listbox"
      aria-label="Select citizen"
    >
      <!-- Citizen Trigger Button -->
      <button
        id="citizen-selector"
        type="button"
        class="inline-flex items-center justify-between w-full px-3 py-2
               bg-white border border-gray-300 rounded-md shadow-sm
               text-sm font-medium text-gray-700
               hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
               transition-colors duration-200"
        on:click={openCitizenDropdown}
        aria-label="Select citizen: {selectedCitizen?.displayName ||
          'ไม่ได้เลือกสมาชิก'}"
      >
        <div class="flex items-center space-x-2 min-w-0 flex-1">
          {#if selectedCitizen}
            <span class="truncate">{selectedCitizen.displayName}</span>
            <span class="text-xs text-gray-500"
              >({selectedCitizen.citizenID})</span
            >
          {:else}
            <span class="text-gray-500">เลือกสมาชิก</span>
          {/if}
        </div>

        <!-- Dropdown Arrow -->
        <svg
          class="w-4 h-4 text-gray-400 transition-transform duration-200
                 {citizenDropdownOpen ? 'transform rotate-180' : ''}"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      <!-- Citizen Dropdown Menu -->
      {#if citizenDropdownOpen}
        <div
          id="citizen-listbox"
          class="absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg
                 max-h-60 overflow-auto"
          role="listbox"
          aria-label="Citizen list"
        >
          <div class="py-1">
            {#each citizenOptions as option}
              <button
                type="button"
                class="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none
                       flex items-center justify-between
                       {option.value === selectedCitizen?.citizenID
                  ? 'bg-blue-50 text-blue-700'
                  : 'text-gray-900'}"
                on:click={() => handleCitizenSelect(option)}
                role="option"
                aria-selected={option.value === selectedCitizen?.citizenID}
              >
                <div class="flex flex-col min-w-0 flex-1">
                  <span class="font-medium">{option.label}</span>
                  <span class="text-xs text-gray-500">{option.value}</span>
                </div>
              </button>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Insurer Selection (Second Step) -->
  <div class="relative {horizontal ? 'flex-1 min-w-0' : ''}">
    {#if showLabels}
      <label
        for="insurer-selector"
        class="block text-sm font-medium text-gray-700 mb-1"
      >
        เลือกบริษัทประกัน
      </label>
    {/if}

    <div
      bind:this={insurerDropdownElement}
      class="relative"
      role="combobox"
      aria-expanded={insurerDropdownOpen}
      aria-haspopup="listbox"
      aria-controls="insurer-listbox"
      aria-label="Select insurer"
    >
      <!-- Insurer Trigger Button -->
      <button
        id="insurer-selector"
        type="button"
        class="inline-flex items-center justify-between w-full px-3 py-2
               bg-white border border-gray-300 rounded-md shadow-sm
               text-sm font-medium text-gray-700
               hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
               transition-colors duration-200
               {!selectedCitizen ? 'opacity-50 cursor-not-allowed' : ''}"
        on:click={openInsurerDropdown}
        disabled={!selectedCitizen}
        aria-label="Select insurer: {selectedInsurer?.displayName ||
          'ไม่ได้เลือกบริษัทประกัน'}"
      >
        <div class="flex items-center space-x-2 min-w-0 flex-1">
          {#if selectedInsurer}
            <span class="truncate">{selectedInsurer.displayName}</span>
            <span class="text-xs text-gray-500"
              >({selectedInsurer.insurerCode})</span
            >
          {:else if selectedCitizen}
            <span class="text-gray-500">เลือกบริษัทประกัน</span>
          {:else}
            <span class="text-gray-400">เลือกสมาชิกก่อน</span>
          {/if}
        </div>

        <!-- Dropdown Arrow -->
        <svg
          class="w-4 h-4 text-gray-400 transition-transform duration-200
                 {insurerDropdownOpen ? 'transform rotate-180' : ''}"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      <!-- Insurer Dropdown Menu -->
      {#if insurerDropdownOpen && selectedCitizen}
        <div
          id="insurer-listbox"
          class="absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg
                 max-h-60 overflow-auto"
          role="listbox"
          aria-label="Insurer list"
        >
          <div class="py-1">
            {#if insurerOptions.length === 0}
              <div class="px-3 py-2 text-sm text-gray-500 text-center">
                ไม่พบบริษัทประกัน
              </div>
            {:else}
              {#each insurerOptions as option}
                <button
                  type="button"
                  class="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none
                         flex items-center justify-between
                         {option.value === selectedInsurer?.insurerCode
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-900'}"
                  on:click={() => handleInsurerSelect(option)}
                  role="option"
                  aria-selected={option.value === selectedInsurer?.insurerCode}
                >
                  <div class="flex flex-col min-w-0 flex-1">
                    <span class="font-medium">{option.label}</span>
                    <span class="text-xs text-gray-500">{option.value}</span>
                  </div>
                </button>
              {/each}
            {/if}
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Selection Status -->
  <!-- {#if selectedMember}
    <div
      class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded {horizontal
        ? 'sm:col-span-2'
        : ''}"
    >
      ✓ เลือกสมาชิกเรียบร้อยแล้ว
    </div>
  {/if} -->
</div>
