<!--
  PolicyList.svelte

  A comprehensive responsive insurance policy list component that displays customer policies
  in an enhanced card layout with detailed information.

  Features:
  - Responsive CSS Grid layout optimized for comprehensive policy information
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges and information sections
  - Professional card design with comprehensive policy details
  - Thai language labels with English code/comments
  - Currency and date formatting for Thai locale
  - Graceful handling of missing data fields

  Enhanced Information Display:
  - Member information (name, contact details, demographics)
  - Policy numbers (policy, certificate, insurance card, staff number)
  - Coverage details (coverage amount, premium with Thai Baht formatting)
  - Plan information (effective dates, plan code)
  - Company information (insurer, company details)
  - Additional metadata (citizen ID, language, citizenship)

  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Small (640px - 768px): 1 card per row
  - Medium (768px - 1024px): 2 cards per row
  - Large (1024px - 1280px): 2 cards per row
  - XL (1280px - 1536px): 3 cards per row
  - 2XL (> 1536px): 3 cards per row

  Usage:
  <PolicyList />

  Future Enhancement: Accept policies as props
  <PolicyList {policies} />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";
  import { policiesStore, loadPolicies } from "./stores/dataStore.js";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
    selectedCitizenStore,
    selectedInsurerStore,
  } from "./stores/memberStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";
  import TwoStepMemberSelector from "./components/TwoStepMemberSelector.svelte";
  import { getApiCitizenId } from "./utils/memberData.js";

  const dispatch = createEventDispatcher();

  // Reactive store subscriptions
  $: policies = $policiesStore.data || [];
  $: loading = $policiesStore.loading;
  $: error = $policiesStore.error;
  $: selectedMember = $selectedMemberStore;
  $: memberDisplayName = $selectedMemberDisplayName;
  $: selectedCitizen = $selectedCitizenStore;
  $: selectedInsurer = $selectedInsurerStore;

  // Check if both citizen and insurer are selected
  $: bothSelected = selectedCitizen && selectedInsurer;

  // Policy type icons mapping - updated for insurance plans
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Life: "❤️",
    Health: "🏥",
    Medical: "🏥",
    Business: "🏢",
    General: "📄",
    Basic: "📋",
    Premium: "⭐",
    Executive: "💼",
  };

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Inactive: "bg-gray-100 text-gray-800 border-gray-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Format date in Thai format
  function formatDate(dateString) {
    if (!dateString) return "ไม่ระบุ";
    const date = new Date(dateString);
    // return date.toLocaleDateString("th-TH", {
    //   day: "2-digit",
    //   month: "2-digit",
    //   year: "numeric",
    // });

    // Convert to Buddhist Era (BE)
    const day = date.getDate().toString().padStart(2, '0');
    const monthName = date.toLocaleDateString('th-TH', { month: 'long' });
    const yearBE = date.getFullYear() + 543;

    return `${day} ${monthName} ${yearBE}`;
  }

  // Format currency in Thai Baht
  function formatCurrency(amount) {
    if (!amount || amount === 0) return "ไม่ระบุ";
    return new Intl.NumberFormat("th-TH", {
      style: "currency",
      currency: "THB",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  // Get policy type display name
  function getPolicyTypeDisplay(planName) {
    if (!planName) return "ไม่ระบุ";

    // Extract type from Thai plan names
    if (planName.includes("สุขภาพ")) return "สุขภาพ";
    if (planName.includes("ชีวิต")) return "ชีวิต";
    if (planName.includes("Executive")) return "เอ็กเซ็กคูทีฟ";
    if (planName.includes("Premium")) return "พรีเมี่ยม";
    if (planName.includes("Basic") || planName.includes("พื้นฐาน"))
      return "พื้นฐาน";

    return "พื้นฐาน";
  }

  // Get policy type icon
  function getPolicyIcon(planName) {
    const type = getPolicyTypeDisplay(planName);
    return typeIcons[type] || typeIcons.General;
  }

  // Handle policy card click to navigate to detail page
  function handlePolicyClick(memberCode) {
    dispatch("navigate", {
      page: "policy-detail",
      memberCode: memberCode,
    });
  }

  // Load policies data based on selected member
  async function loadPoliciesData() {
    if (!selectedMember) {
      console.warn("No member selected, cannot load policies");
      policiesStore.setError(new Error("กรุณาเลือกสมาชิกก่อนดูข้อมูลกรมธรรม์"));
      return;
    }

    if (!selectedMember?.insurerCode || !selectedMember?.citizenID) {
      console.warn("Selected member missing required data for API call");
      policiesStore.setError(new Error("ข้อมูลสมาชิกไม่ครบถ้วน"));
      return;
    }

    try {
      // Convert citizen code to API citizen ID for API call
      const apiCitizenId = getApiCitizenId(selectedMember.citizenID);

      const searchParams = {
        INSURER_CODE: selectedMember.insurerCode,
        CITIZEN_ID: apiCitizenId,
      };

      console.log(`Making API call with params:`, {
        insurerCode: selectedMember.insurerCode,
        citizenId: apiCitizenId,
        originalCitizenCode: selectedMember.citizenID,
      });

      await loadPolicies(searchParams);
      console.log(
        `Policies loaded successfully for member ${selectedMember?.memberCode}:`,
        $policiesStore.data?.length || 0,
      );
    } catch (error) {
      console.error("Failed to load policies:", error);
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPoliciesData();
  }

  // Reactive statement to reload policies when member changes
  $: if (selectedMember) {
    loadPoliciesData();
  }

  onMount(() => {
    console.log("PolicyList component mounted");
    // Initial load will be triggered by reactive statement when member is available
  });
</script>

<main
  class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8"
  aria-label="Insurance Policy List"
>
  <div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <header class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-6">กรมธรรม์ประกันภัย</h1>

      <!-- Two-Step Member Selector -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <!-- <h2 class="text-lg font-semibold text-gray-900 mb-4">
          เลือกข้อมูลสมาชิก
        </h2> -->
        <TwoStepMemberSelector
          compact={false}
          showLabels={true}
          horizontal={true}
        />
      </div>

      <!-- Selection Status -->
      {#if selectedMember}
        <!-- <p class="text-gray-600">
          กรมธรรม์ของ <span class="font-semibold text-gray-800"
            >{memberDisplayName}</span
          >
          <span class="text-sm text-gray-500 ml-2"
            >(รหัส: {selectedMember?.memberCode})</span
          >
        </p> -->
      {:else if selectedCitizen && selectedInsurer}
        <p class="text-gray-600">กำลังโหลดข้อมูลสมาชิก...</p>
      <!-- {:else}
        <p class="text-gray-600">
          กรุณาเลือกสมาชิกและบริษัทประกันเพื่อดูข้อมูลกรมธรรม์
        </p> -->
      {/if}
    </header>

    <!-- No Selection State -->
    {#if !bothSelected}
      <div class="text-center py-12">
        <div class="text-6xl mb-4" aria-hidden="true">👤</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          กรุณาเลือกข้อมูลสมาชิก
        </h2>
        <p class="text-gray-600 mb-6">
          กรุณาเลือกสมาชิกและบริษัทประกันจากด้านบนเพื่อดูข้อมูลกรมธรรม์
        </p>
      </div>

      <!-- Loading State -->
    {:else if loading}
      <LoadingStates
        variant="cards"
        count={6}
        message="กำลังโหลดข้อมูลกรมธรรม์..."
      />

      <!-- Error State -->
    {:else if error}
      <ErrorStates
        variant="api"
        {error}
        on:retry={handleRetry}
        message="ไม่สามารถโหลดข้อมูลกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"
      />

      <!-- Empty State -->
    {:else if policies.length === 0}
      <div class="text-center py-12">
        <div class="text-6xl mb-4" aria-hidden="true">📄</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          ไม่พบข้อมูลกรมธรรม์
        </h2>
        <p class="text-gray-600 mb-6">
          ไม่พบกรมธรรม์ประกันภัยสำหรับสมาชิกท่านนี้
        </p>
        <button
          class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          on:click={handleRetry}
          aria-label="Refresh policies"
        >
          <svg
            class="mr-2 w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          รีเฟรช
        </button>
      </div>

      <!-- Policies Grid -->
    {:else}
      <!-- Member Information -->
      <section class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">ข้อมูลสมาชิก</h2>
        <div class="grid gap-4 sm:grid-cols-2">
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              ชื่อ-นามสกุล (ไทย)
            </div>
            <p class="text-gray-900">
              {selectedMember?.titleTH || ""}
              {selectedMember?.nameTH || ""}
              {selectedMember?.surnameTH || ""}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              ชื่อ-นามสกุล (อังกฤษ)
            </div>
            <p class="text-gray-900">
              {selectedMember?.titleEN || ""}
              {selectedMember?.nameEN || ""}
              {selectedMember?.surnameEN || ""}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">
              เลขบัตรประชาชน
            </div>
            <p class="text-gray-900 font-mono">
              {selectedMember?.citizenID || "ไม่ระบุ"}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">วันเกิด</div>
            <p class="text-gray-900">
              {formatDate(policies[0]?.BirthDate)}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">เพศ</div>
            <p class="text-gray-900">
              {policies[0].Gender === "M"
                ? "ชาย"
                : policies[0].Gender === "F"
                  ? "หญิง"
                  : "ไม่ระบุ"}
            </p>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-500 mb-1">สัญชาติ</div>
            <p class="text-gray-900">
              {policies[0].Citizenship === "Thai"
                ? "ไทย"
                : policies[0].Citizenship}
            </p>
          </div>
          {#if policies[0].CompanyName && policies[0].CompanyName !== policies[0].InsurerName}
            <div>
              <!-- <span class="text-sm text-gray-500">บริษัท</span> -->
              <div class="text-sm font-medium text-gray-500 mb-1">
                บริษัทคู่สัญญา
              </div>
              <p class="text-gray-900">
                {policies[0].CompanyName || policies[0].CompanyNameEN}
              </p>
            </div>
          {/if}
        </div>
      </section>

      <p class="pt-10 text-gray-600">
        กรมธรรม์ของ <span class="font-semibold text-gray-800"
          >{memberDisplayName}</span
        >
        {#if selectedMember?.memberCode}
          <span class="text-sm text-gray-500 ml-2"
            >(รหัส: {selectedMember.memberCode})</span
          >
        {/if}
      </p>
      <section
        class="grid gap-6 py-6
             grid-cols-1
             sm:grid-cols-1
             md:grid-cols-3
             lg:grid-cols-3
             xl:grid-cols-4
             2xl:grid-cols-4"
        aria-label="Policy cards grid"
      >
        {#each policies as policy (policy.MemberCode || policy.PolicyNo)}
          <button
            class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[480px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
            aria-labelledby="policy-{policy.MemberCode}-title"
            on:click={() => handlePolicyClick(policy.MemberCode)}
          >
            <!-- Policy Header -->
            <div class="mb-4">
              <div class="flex items-center justify-between mb-3">
                <!-- <p class="text-xl font-semibold text-gray-900 mb-4">
                  {policy.PlanName}
                </p> -->
                <h2
                  id="policy-{policy.MemberCode}-title"
                  class="text-lg font-semibold text-gray-900 flex items-center gap-2"
                >
                  <span class="text-2xl" aria-hidden="true"
                    >{getPolicyIcon(policy.PlanName)}</span
                  >
                  {getPolicyTypeDisplay(policy.PlanName)}
                  <!-- {policy.PlanName} -->
                </h2>
                <div class="flex flex-row items-end gap-1">
                  <span
                    class="px-3 py-1 rounded-full text-xs font-medium border
                       {statusColors[policy.MemberStatus] ||
                      statusColors.Active}"
                    aria-label="Member status: {policy.MemberStatus}"
                  >
                    {policy.MemberStatus}
                  </span>
                  {#if policy.VIP === "Y"}
                    <span
                      class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200"
                    >
                      VIP
                    </span>
                  {/if}
                </div>
              </div>

              <!-- Member Information -->
              <!-- <div class="mb-3">
                <p class="text-sm font-medium text-gray-900 mb-1">
                  {getMemberDisplayName(policy)}
                </p>
                {#if getContactInfo(policy)}
                  <p class="text-xs text-gray-500">
                    {getContactInfo(policy)}
                  </p>
                {/if}
              </div> -->

              <!-- Policy Numbers -->
              <div class="space-y-1 mb-3">
                <p
                  class="text-sm text-gray-600 flex justify-between items-center"
                >
                  <span class="text-sm text-gray-500">เลขที่กรมธรรม์</span>
                  <span class="text-sm font-medium text-gray-900 text-right">
                    {policy.PolicyNo}
                  </span>
                </p>
                <p
                  class="text-sm text-gray-600 flex justify-between items-center"
                >
                  <span class="text-sm text-gray-500">เลขที่ใบรับรอง</span>
                  <span class="text-sm font-medium text-gray-900 text-right">
                    {policy.CertificateNo}
                  </span>
                </p>
                {#if policy.InsurerCardNo}
                  <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                  >
                    <span class="text-sm text-gray-500">เลขบัตรประกัน</span>
                    <span class="text-sm font-medium text-gray-900 text-right">
                      {policy.InsurerCardNo}
                    </span>
                  </p>
                {/if}
                {#if policy.StaffNo}
                  <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                  >
                    <span class="text-sm text-gray-500">รหัสพนักงานตัวแทน</span>
                    <span class="text-sm font-medium text-gray-900 text-right">
                      {policy.StaffNo}
                    </span>
                  </p>
                {/if}
              </div>
            </div>

            <!-- Policy Details -->
            <div class="space-y-4 mb-4 flex-grow">
              <!-- Member & Card Information -->
              <div class="bg-gray-50 rounded-lg p-3 space-y-2">
                <h3
                  class="text-xs font-semibold text-gray-700 uppercase tracking-wide"
                >
                  ข้อมูลสมาชิก
                </h3>
                <div class="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span class="text-gray-500">ประเภทสมาชิก</span>
                    <div class="font-medium text-gray-900">
                      {policy.MemberType}
                    </div>
                  </div>
                  <div>
                    <span class="text-gray-500">ประเภทบัตร</span>
                    <div class="font-medium text-gray-900">
                      {policy.CardType}
                    </div>
                  </div>
                  <!-- {#if policy.Gender}
                    <div>
                      <span class="text-gray-500">เพศ:</span>
                      <div class="font-medium text-gray-900">
                        {policy.Gender === "M" ? "ชาย" : "หญิง"}
                      </div>
                    </div>
                  {/if}
                  {#if policy.BirthDate}
                    <div>
                      <span class="text-gray-500">วันเกิด:</span>
                      <div class="font-medium text-gray-900">
                        {formatDate(policy.BirthDate)}
                      </div>
                    </div>
                  {/if} -->
                </div>
              </div>

              <!-- Coverage & Premium Information -->
              {#if policy.CoverageAmount || policy.Premium}
                <div class="bg-blue-50 rounded-lg p-3 space-y-2">
                  <h3
                    class="text-xs font-semibold text-blue-700 uppercase tracking-wide"
                  >
                    ความคุ้มครอง
                  </h3>
                  <div class="space-y-2 text-xs">
                    {#if policy.CoverageAmount}
                      <div class="flex justify-between items-center">
                        <span class="text-blue-600">วงเงินคุ้มครอง</span>
                        <span class="font-semibold text-blue-900">
                          {formatCurrency(policy.CoverageAmount)}
                        </span>
                      </div>
                    {/if}
                    {#if policy.Premium}
                      <div class="flex justify-between items-center">
                        <span class="text-blue-600">เบี้ยประกัน</span>
                        <span class="font-semibold text-blue-900">
                          {formatCurrency(policy.Premium)}
                        </span>
                      </div>
                    {/if}
                  </div>
                </div>
              {/if}

              <!-- Plan & Date Information -->
              <div class="bg-green-50 rounded-lg p-3 space-y-2">
                <h3
                  class="text-xs font-semibold text-green-700 uppercase tracking-wide"
                >
                  ระยะเวลาคุ้มครอง
                </h3>
                <div class="space-y-2 text-xs">
                  <div class="flex justify-between items-center">
                    <span class="text-green-600">วันที่เริ่มต้น</span>
                    <span class="font-medium text-green-900">
                      {formatDate(policy.PlanEffFrom)}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-green-600">วันที่สิ้นสุด</span>
                    <span class="font-medium text-green-900">
                      {formatDate(policy.PlanEffTo)}
                    </span>
                  </div>
                  {#if policy.PlanCode}
                    <div class="flex justify-between items-center">
                      <span class="text-green-600">รหัสแผน:</span>
                      <span class="font-medium text-green-900">
                        {policy.PlanCode}
                      </span>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Company Information -->
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-500">บริษัทประกัน</span>
                  <span class="text-sm font-medium text-gray-900 text-right">
                    {policy.InsurerName || policy.InsurerNameEN}
                  </span>
                </div>
              </div>
            </div>

            <!-- Plan Description -->
            <div class="border-t border-gray-100 pt-4">
              <div class="space-y-2">
                <p class="text-xs text-gray-600 leading-relaxed line-clamp-2">
                  รหัสแผน:
                  <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                    {policy.PlanCode}
                  </span>
                </p>
                <p class="text-xs text-gray-600 leading-relaxed line-clamp-2">
                  ชื่อแผน:
                  <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                    {policy.PlanName}
                  </span>
                </p>

                <!-- Additional Information -->
                <!-- <div class="flex flex-wrap gap-2 text-xs"> -->
                <!-- {#if policy.CitizenID}
                    <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                      เลขบัตรประชาชน: {policy.CitizenID}
                    </span>
                  {/if} -->
                <!-- {#if policy.Language}
                    ภาษา:
                    <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                      {policy.Language === "TH" ? "ไทย" : "อังกฤษ"}
                    </span>
                  {/if} -->
                <!-- {#if policy.Citizenship}
                    <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                      สัญชาติ: {policy.Citizenship === "Thai"
                        ? "ไทย"
                        : policy.Citizenship}
                    </span>
                  {/if} -->
                <!-- </div> -->

                <!-- Member Code for Reference -->
                <!-- <p class="text-xs text-gray-400 mt-2">
                  รหัสสมาชิก: {policy.MemberCode}
                </p> -->
              </div>
            </div>
          </button>
        {/each}
      </section>
    {/if}
  </div>
</main>
