<script>
  import { onMount } from "svelte";
  import PolicyList from "./PolicyList.svelte";
  import PolicyDetail from "./PolicyDetail.svelte";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
    selectedMemberShortName,
    initializeMemberStore,
  } from "./stores/memberStore.js";

  // Application metadata
  const appInfo = {
    name: "Insurance Portal",
    version: "1.0.0",
    description:
      "Comprehensive insurance management platform built with Svelte and Tailwind CSS",
  };

  // Navigation state
  let currentPage = "policy-list";
  let selectedPolicyId = null;
  let selectedMemberCode = null;

  // Navigation handler
  function handleNavigation(event) {
    currentPage = event.detail.page;
    selectedPolicyId = event.detail.policyId || null;
    selectedMemberCode = event.detail.memberCode || null;
    console.log(
      "Navigating to:",
      currentPage,
      selectedPolicyId ? `with policy ID: ${selectedPolicyId}` : "",
      selectedMemberCode ? `with member code: ${selectedMemberCode}` : "",
    );
  }

  // Page title mapping
  const pageTitles = {
    "policy-list": "กรมธรรม์ประกันภัย",
    "policy-detail": "กรมธรรม์ประกันภัย",
  };

  // Update document title when page changes
  $: if (typeof document !== "undefined") {
    document.title = pageTitles[currentPage] || "Insurance Portal";
  }

  // Initialize member store on app startup
  onMount(async () => {
    try {
      await initializeMemberStore();
      console.log("Member store initialized successfully");
    } catch (error) {
      console.error("Failed to initialize member store:", error);
    }
  });

  console.log("App initialized:", appInfo);
</script>

<div class="min-h-screen bg-gray-50">
  <!-- Navigation Header -->
  <!-- <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-0 sm:px-0 lg:px-0">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center space-x-4 lg:space-x-6 flex-1 min-w-0">
          Logo -->
          <!-- <button
            class="text-xl font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1 flex-shrink-0"
            on:click={() =>
              handleNavigation({ detail: { page: "policy-list" } })}
            aria-label="Go to home page"
          >
            📃
          </button> -->

          <!-- Breadcrumb -->
          <!-- {#if currentPage !== "policy-list"}
            <span class="text-gray-300 flex-shrink-0" aria-hidden="true">/</span
            >
            <span class="text-sm text-gray-600 capitalize flex-shrink-0">
              {currentPage.replace("-", " ")}
            </span>
          {/if}
        </div> -->

        <!-- <div class="flex items-center space-x-4 flex-shrink-0"> -->
          <!-- Welcome Message -->
          <!-- <div class="hidden lg:flex items-center space-x-2">
            <span class="text-sm text-gray-500">Welcome back,</span>
            <span class="text-sm font-medium text-gray-700">
              {$selectedMemberShortName || "Guest"}
            </span>
          </div> -->

          <!-- User Avatar -->
          <!-- <div
            class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"
            title={$selectedMemberDisplayName || "No member selected"}
          >
            <span class="text-white text-sm font-medium">
              {$selectedMemberStore
                ? $selectedMemberStore.nameEN
                  ? $selectedMemberStore.nameEN.charAt(0)
                  : $selectedMemberStore.nameTH
                    ? $selectedMemberStore.nameTH.charAt(0)
                    : $selectedMemberStore.memberCode.charAt(3)
                : "G"}
            </span>
          </div> -->
        <!-- </div>
      </div>
    </div>
  </nav> -->

  <!-- Main Content -->
  <div class="flex-1">
    {#if currentPage === "policy-list"}
      <PolicyList on:navigate={handleNavigation} />
    {:else if currentPage === "policy-detail"}
      <PolicyDetail
        {selectedPolicyId}
        {selectedMemberCode}
        on:navigate={handleNavigation}
      />
    {:else}
      <!-- Fallback to policy list -->
      <PolicyList on:navigate={handleNavigation} />
    {/if}
  </div>

  <!-- Footer -->
  <!-- <footer class="bg-white border-t border-gray-200 mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="text-sm text-gray-500 mb-4 md:mb-0">
          © 2024 Insurance Portal. All rights reserved.
        </div>
        <div class="flex space-x-6 text-sm text-gray-500">
          <button
            class="hover:text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="View privacy policy"
          >
            Privacy Policy
          </button>
          <button
            class="hover:text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="View terms of service"
          >
            Terms of Service
          </button>
          <button
            class="hover:text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="Contact support"
          >
            Support
          </button>
        </div>
      </div>
    </div>
  </footer> -->
</div>
