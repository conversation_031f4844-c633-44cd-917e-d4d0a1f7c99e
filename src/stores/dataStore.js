/**
 * dataStore.js
 * 
 * Centralized data management store for API data with caching and state management.
 * 
 * Features:
 * - Svelte stores for reactive data management
 * - In-memory caching with TTL (Time To Live)
 * - Loading and error state management
 * - Automatic cache invalidation
 * - Retry mechanisms for failed requests
 * 
 * Usage:
 * import { policiesStore, loadPolicies } from './stores/dataStore.js';
 */

import { writable, derived, get } from 'svelte/store';
import api from '../api/index.js';
import { selectedMemberStore } from './memberStore.js';
import { getApiCitizenId } from '../utils/memberData.js';


// Cache configuration
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
const MAX_RETRY_ATTEMPTS = 3;

// Create cache storage
const cache = new Map();

// Helper function to create cache key with member context
function createCacheKey(type, params = {}, includeMemberContext = true) {
  let keyParts = [type];

  // Add member context if requested and available
  if (includeMemberContext) {
    const currentMember = get(selectedMemberStore);
    if (currentMember && currentMember.memberCode) {
      keyParts.push(`member:${currentMember.memberCode}`);
    }
  }

  // Add other parameters
  const paramString = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');

  if (paramString) {
    keyParts.push(paramString);
  }

  return keyParts.join('_');
}

// Helper function to check if cache entry is valid
function isCacheValid(entry) {
  return entry && (Date.now() - entry.timestamp) < CACHE_TTL;
}

// Helper function to get from cache
function getFromCache(key) {
  const entry = cache.get(key);
  if (isCacheValid(entry)) {
    return entry.data;
  }
  cache.delete(key);
  return null;
}

// Helper function to set cache
function setCache(key, data) {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
}

// Create base store structure
function createDataStore(initialState = {}) {
  const { subscribe, set, update } = writable({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
    ...initialState
  });

  return {
    subscribe,
    set,
    update,
    setLoading: (loading) => update(state => ({ ...state, loading, error: loading ? null : state.error })),
    setData: (data) => update(state => ({ ...state, data, loading: false, error: null, lastUpdated: Date.now() })),
    setError: (error) => update(state => ({ ...state, error, loading: false })),
    reset: () => set({ data: null, loading: false, error: null, lastUpdated: null })
  };
}

// Policies store
export const policiesStore = createDataStore();

// Policy detail store
export const policyDetailStore = createDataStore();

// Derived stores for computed values
export const activePolicies = derived(
  policiesStore,
  $policies => $policies.data?.filter(policy => policy.status === 'Active') || []
);



// Load policies function with member context
export async function loadPolicies(params = {}, forceRefresh = false) {
  const currentMember = get(selectedMemberStore);
  const cacheKey = createCacheKey('policies', params, true);

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      policiesStore.setData(cachedData);
      return cachedData;
    }
  }

  policiesStore.setLoading(true);

  try {
    let result;

    // Use provided params first, then fall back to member context
    if (params.INSURER_CODE && params.CITIZEN_ID) {
      // Use provided insurer code and citizen ID (uppercase format)
      result = await api.policies.searchByCitizenId(params.CITIZEN_ID, params.INSURER_CODE);
    } else if (params.insurerCode && params.citizenId) {
      // Use provided insurer code and citizen ID (camelCase format - legacy)
      result = await api.policies.searchByCitizenId(params.citizenId, params.insurerCode);
    } else if (params.CITIZEN_ID) {
      // Use provided citizen ID with default insurer (uppercase format)
      result = await api.policies.searchByCitizenId(params.CITIZEN_ID);
    } else if (params.citizenId) {
      // Use provided citizen ID with default insurer (camelCase format - legacy)
      result = await api.policies.searchByCitizenId(params.citizenId);
    } else if (params.policyNumber && params.name) {
      result = await api.policies.searchByPolicyAndName(params.policyNumber, params.name);
    } else if (params.name) {
      result = await api.policies.searchByName(params.name);
    } else if (currentMember && currentMember.citizenID && currentMember.insurerCode) {
      // Use member's citizen ID and insurer code for policy search (convert citizen code to API ID)
      const apiCitizenId = getApiCitizenId(currentMember.citizenID);
      result = await api.policies.searchByCitizenId(apiCitizenId, currentMember.insurerCode);
    } else if (currentMember && currentMember.citizenID) {
      // Fallback to current member's citizen ID with default insurer (convert citizen code to API ID)
      const apiCitizenId = getApiCitizenId(currentMember.citizenID);
      result = await api.policies.searchByCitizenId(apiCitizenId);
    } else {
      // Default search for demo
      result = await api.policies.searchByCitizenId('1234567890123');
    }

    if (result.success && result.data) {
      const policies = result.data;
      setCache(cacheKey, policies);
      policiesStore.setData(policies);
      return policies;
    } else {
      throw new Error(result.message || 'Failed to load policies');
    }
  } catch (error) {
    console.error('Error loading policies:', error);
    policiesStore.setError(error);
    throw error;
  }
}

// Load policy detail function
export async function loadPolicyDetail(memberCode, forceRefresh = false) {
  if (!memberCode) {
    const error = new Error('Member code is required');
    policyDetailStore.setError(error);
    throw error;
  }

  const cacheKey = createCacheKey('policy_detail', { memberCode });

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      policyDetailStore.setData(cachedData);
      return cachedData;
    }
  }

  policyDetailStore.setLoading(true);

  try {
    const result = await api.members.getPolicyDetail(memberCode);

    if (result.success && result.data) {
      const policyDetail = result.data;
      setCache(cacheKey, policyDetail);
      policyDetailStore.setData(policyDetail);
      return policyDetail;
    } else {
      throw new Error(result.message || 'Failed to load policy details');
    }
  } catch (error) {
    console.error('Error loading policy detail:', error);
    policyDetailStore.setError(error);
    throw error;
  }
}



// Utility functions
export function clearCache() {
  cache.clear();
}

export function clearCacheByType(type) {
  for (const [key] of cache) {
    if (key.startsWith(type)) {
      cache.delete(key);
    }
  }
}

export function refreshAllData() {
  clearCache();
  // Reset all stores
  policiesStore.reset();
  policyDetailStore.reset();
}

// Retry function with exponential backoff
export async function retryOperation(operation, maxAttempts = MAX_RETRY_ATTEMPTS) {
  let lastError;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts) {
        break;
      }

      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = Math.pow(2, attempt - 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
