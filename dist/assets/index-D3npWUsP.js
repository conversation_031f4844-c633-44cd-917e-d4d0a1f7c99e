var Wi=Object.defineProperty;var Qi=(t,e,l)=>e in t?Wi(t,e,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[e]=l;var hl=(t,e,l)=>Qi(t,typeof e!="symbol"?e+"":e,l);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&n(s)}).observe(document,{childList:!0,subtree:!0});function l(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=l(i);fetch(i.href,o)}})();function q(){}function ki(t){return t()}function Xl(){return Object.create(null)}function Et(t){t.forEach(ki)}function Dl(t){return typeof t=="function"}function Ht(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}function Xi(t){return Object.keys(t).length===0}function Pl(t,...e){if(t==null){for(const n of e)n(void 0);return q}const l=t.subscribe(...e);return l.unsubscribe?()=>l.unsubscribe():l}function tl(t){let e;return Pl(t,l=>e=l)(),e}function ot(t,e,l){t.$$.on_destroy.push(Pl(e,l))}function r(t,e){t.appendChild(e)}function k(t,e,l){t.insertBefore(e,l||null)}function E(t){t.parentNode&&t.parentNode.removeChild(t)}function Ke(t,e){for(let l=0;l<t.length;l+=1)t[l]&&t[l].d(e)}function d(t){return document.createElement(t)}function te(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function y(t){return document.createTextNode(t)}function g(){return y(" ")}function pl(){return y("")}function Se(t,e,l,n){return t.addEventListener(e,l,n),()=>t.removeEventListener(e,l,n)}function a(t,e,l){l==null?t.removeAttribute(e):t.getAttribute(e)!==l&&t.setAttribute(e,l)}function er(t){return Array.from(t.childNodes)}function T(t,e){e=""+e,t.data!==e&&(t.data=e)}function tr(t,e,{bubbles:l=!1,cancelable:n=!1}={}){return new CustomEvent(t,{detail:e,bubbles:l,cancelable:n})}let el;function Xt(t){el=t}function Ti(){if(!el)throw new Error("Function called outside component initialization");return el}function ul(t){Ti().$$.on_mount.push(t)}function Al(){const t=Ti();return(e,l,{cancelable:n=!1}={})=>{const i=t.$$.callbacks[e];if(i){const o=tr(e,l,{cancelable:n});return i.slice().forEach(s=>{s.call(t,o)}),!o.defaultPrevented}return!0}}const Ut=[],dl=[];let Yt=[];const en=[],lr=Promise.resolve();let kl=!1;function nr(){kl||(kl=!0,lr.then(Ii))}function Tl(t){Yt.push(t)}const yl=new Set;let Bt=0;function Ii(){if(Bt!==0)return;const t=el;do{try{for(;Bt<Ut.length;){const e=Ut[Bt];Bt++,Xt(e),ir(e.$$)}}catch(e){throw Ut.length=0,Bt=0,e}for(Xt(null),Ut.length=0,Bt=0;dl.length;)dl.pop()();for(let e=0;e<Yt.length;e+=1){const l=Yt[e];yl.has(l)||(yl.add(l),l())}Yt.length=0}while(Ut.length);for(;en.length;)en.pop()();kl=!1,yl.clear(),Xt(t)}function ir(t){if(t.fragment!==null){t.update(),Et(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Tl)}}function rr(t){const e=[],l=[];Yt.forEach(n=>t.indexOf(n)===-1?e.push(n):l.push(n)),l.forEach(n=>n()),Yt=e}const al=new Set;let zt;function Rl(){zt={r:0,c:[],p:zt}}function Ol(){zt.r||Et(zt.c),zt=zt.p}function He(t,e){t&&t.i&&(al.delete(t),t.i(e))}function Ue(t,e,l,n){if(t&&t.o){if(al.has(t))return;al.add(t),zt.c.push(()=>{al.delete(t),n&&(l&&t.d(1),n())}),t.o(e)}else n&&n()}function ue(t){return(t==null?void 0:t.length)!==void 0?t:Array.from(t)}function sr(t,e){t.d(1),e.delete(t.key)}function ar(t,e,l,n,i,o,s,c,u,f,m,p){let b=t.length,v=o.length,_=b;const C={};for(;_--;)C[t[_].key]=_;const h=[],w=new Map,S=new Map,x=[];for(_=v;_--;){const z=p(i,o,_),N=l(z);let A=s.get(N);A?x.push(()=>A.p(z,e)):(A=f(N,z),A.c()),w.set(N,h[_]=A),N in C&&S.set(N,Math.abs(_-C[N]))}const V=new Set,R=new Set;function H(z){He(z,1),z.m(c,m),s.set(z.key,z),m=z.first,v--}for(;b&&v;){const z=h[v-1],N=t[b-1],A=z.key,O=N.key;z===N?(m=z.first,b--,v--):w.has(O)?!s.has(A)||V.has(A)?H(z):R.has(O)?b--:S.get(A)>S.get(O)?(R.add(A),H(z)):(V.add(O),b--):(u(N,s),b--)}for(;b--;){const z=t[b];w.has(z.key)||u(z,s)}for(;v;)H(h[v-1]);return Et(x),h}function kt(t){t&&t.c()}function mt(t,e,l){const{fragment:n,after_update:i}=t.$$;n&&n.m(e,l),Tl(()=>{const o=t.$$.on_mount.map(ki).filter(Dl);t.$$.on_destroy?t.$$.on_destroy.push(...o):Et(o),t.$$.on_mount=[]}),i.forEach(Tl)}function pt(t,e){const l=t.$$;l.fragment!==null&&(rr(l.after_update),Et(l.on_destroy),l.fragment&&l.fragment.d(e),l.on_destroy=l.fragment=null,l.ctx=[])}function or(t,e){t.$$.dirty[0]===-1&&(Ut.push(t),nr(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function Zt(t,e,l,n,i,o,s=null,c=[-1]){const u=el;Xt(t);const f=t.$$={fragment:null,ctx:[],props:o,update:q,not_equal:i,bound:Xl(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(u?u.$$.context:[])),callbacks:Xl(),dirty:c,skip_bound:!1,root:e.target||u.$$.root};s&&s(f.root);let m=!1;if(f.ctx=l?l(t,e.props||{},(p,b,...v)=>{const _=v.length?v[0]:b;return f.ctx&&i(f.ctx[p],f.ctx[p]=_)&&(!f.skip_bound&&f.bound[p]&&f.bound[p](_),m&&or(t,p)),b}):[],f.update(),m=!0,Et(f.before_update),f.fragment=n?n(f.ctx):!1,e.target){if(e.hydrate){const p=er(e.target);f.fragment&&f.fragment.l(p),p.forEach(E)}else f.fragment&&f.fragment.c();e.intro&&He(t.$$.fragment),mt(t,e.target,e.anchor),Ii()}Xt(u)}class Jt{constructor(){hl(this,"$$");hl(this,"$$set")}$destroy(){pt(this,1),this.$destroy=q}$on(e,l){if(!Dl(l))return q;const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(l),()=>{const i=n.indexOf(l);i!==-1&&n.splice(i,1)}}$set(e){this.$$set&&!Xi(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const cr="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(cr);const Ft=[];function ur(t,e){return{subscribe:Lt(t,e).subscribe}}function Lt(t,e=q){let l;const n=new Set;function i(c){if(Ht(t,c)&&(t=c,l)){const u=!Ft.length;for(const f of n)f[1](),Ft.push(f,t);if(u){for(let f=0;f<Ft.length;f+=2)Ft[f][0](Ft[f+1]);Ft.length=0}}}function o(c){i(c(t))}function s(c,u=q){const f=[c,u];return n.add(f),n.size===1&&(l=e(i,o)||q),c(t),()=>{n.delete(f),n.size===0&&l&&(l(),l=null)}}return{set:i,update:o,subscribe:s}}function Tt(t,e,l){const n=!Array.isArray(t),i=n?[t]:t;if(!i.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=e.length<2;return ur(l,(s,c)=>{let u=!1;const f=[];let m=0,p=q;const b=()=>{if(m)return;p();const _=e(n?f[0]:f,s,c);o?s(_):p=Dl(_)?_:q},v=i.map((_,C)=>Pl(_,h=>{f[C]=h,m&=~(1<<C),u&&b()},()=>{m|=1<<C}));return u=!0,b(),function(){Et(v),p(),u=!1}})}const dr={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_ENVIRONMENT:"development",VITE_DEV_MODE:"true",VITE_TPA_API_BASE_URL:"http://localhost:9000",VITE_TPA_API_PREFIX:"/api",VITE_TPA_API_RETRY_ATTEMPTS:"3",VITE_TPA_API_TIMEOUT:"30000"};let ct={};try{ct=dr||{}}catch{ct={}}const _l={devMode:ct.VITE_DEV_MODE==="true",apiEnvironment:ct.VITE_API_ENVIRONMENT||"development",mode:ct.MODE||"development",isDev:ct.DEV!==!1,isProd:ct.PROD===!0},Ct={baseUrl:ct.VITE_TPA_API_BASE_URL||"http://localhost:9000",prefix:ct.VITE_TPA_API_PREFIX||"/api",timeout:parseInt(ct.VITE_TPA_API_TIMEOUT||"30000"),retryAttempts:parseInt(ct.VITE_TPA_API_RETRY_ATTEMPTS||"3")},Ni=()=>`${Ct.baseUrl}${Ct.prefix}`,fr=()=>{const t=[];return(isNaN(Ct.timeout)||Ct.timeout<=0)&&t.push("TPA_API_TIMEOUT must be a positive number"),(isNaN(Ct.retryAttempts)||Ct.retryAttempts<0)&&t.push("TPA_API_RETRY_ATTEMPTS must be a non-negative number"),{isValid:t.length===0,errors:t}};if(_l.devMode&&_l.isDev){console.log("Environment Configuration:",{client:_l,tpaApi:Ct,fullApiUrl:Ni()});const t=fr();t.isValid||console.warn("Environment validation errors:",t.errors)}class ol extends Error{constructor(e,l=null,n=null){super(e),this.name="ApiError",this.status=l,this.data=n,this.timestamp=new Date().toISOString()}getUserMessage(){return this.status===400?"Invalid request. Please check your input and try again.":this.status===404?"The requested information was not found.":this.status===422?"The provided data is invalid. Please check your input.":this.status>=500?"Server error. Please try again later.":this.message||"An unexpected error occurred."}isRetryable(){return this.status>=500}toJSON(){return{name:this.name,message:this.message,status:this.status,data:this.data,timestamp:this.timestamp}}}class cl extends Error{constructor(e="Network error"){super(e),this.name="NetworkError",this.timestamp=new Date().toISOString()}getUserMessage(){return"Unable to connect to the server. Please check your internet connection and try again."}isRetryable(){return!0}toJSON(){return{name:this.name,message:this.message,timestamp:this.timestamp}}}class Il extends Error{constructor(e="Request timeout"){super(e),this.name="TimeoutError",this.timestamp=new Date().toISOString()}getUserMessage(){return"The request took too long to complete. Please try again."}isRetryable(){return!0}toJSON(){return{name:this.name,message:this.message,timestamp:this.timestamp}}}class xi extends Error{constructor(e,l=null,n=null){super(e),this.name="ValidationError",this.field=l,this.value=n,this.timestamp=new Date().toISOString()}getUserMessage(){return this.field?`Invalid value for ${this.field}. ${this.message}`:this.message||"Invalid input provided."}isRetryable(){return!1}toJSON(){return{name:this.name,message:this.message,field:this.field,value:this.value,timestamp:this.timestamp}}}function mr(t,e={}){const l={type:t.name||"Error",message:t.message||"Unknown error",userMessage:t.getUserMessage?t.getUserMessage():t.message,isRetryable:t.isRetryable?t.isRetryable():!1,timestamp:t.timestamp||new Date().toISOString(),context:e};return t.status&&(l.status=t.status),t.data&&(l.data=t.data),t.field&&(l.field=t.field),t.retryAfter&&(l.retryAfter=t.retryAfter),l}function It(t,e={}){const l=mr(t,e);t instanceof xi?console.warn("Validation Error:",l):t instanceof cl||t instanceof Il?console.warn("Network Error:",l):t instanceof ol&&t.status>=500?console.error("Server Error:",l):console.error("API Error:",l)}class pr{constructor(e={}){this.baseUrl=e.baseUrl||Ni(),this.timeout=e.timeout||Ct.timeout,this.retryAttempts=e.retryAttempts||Ct.retryAttempts,this.retryDelay=e.retryDelay||1e3,this.requestInterceptors=[],this.responseInterceptors=[]}addRequestInterceptor(e){this.requestInterceptors.push(e)}addResponseInterceptor(e){this.responseInterceptors.push(e)}async applyRequestInterceptors(e){let l={...e};for(const n of this.requestInterceptors)l=await n(l);return l}async applyResponseInterceptors(e){let l=e;for(const n of this.responseInterceptors)l=await n(l);return l}calculateRetryDelay(e){return this.retryDelay*Math.pow(2,e)+Math.random()*1e3}isRetryableError(e){return e instanceof cl||e instanceof Il?!0:e instanceof ol?e.status>=500:!1}createTimeoutController(){const e=new AbortController,l=setTimeout(()=>{e.abort()},this.timeout);return{controller:e,timeoutId:l}}async request(e,l={}){const n=`${this.baseUrl}${e}`;let i;const o=await this.applyRequestInterceptors({url:n,...l});for(let s=0;s<=this.retryAttempts;s++){const{controller:c,timeoutId:u}=this.createTimeoutController();try{const f={method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...o.headers},signal:c.signal,...o};delete f.url;const m=await fetch(o.url,f);clearTimeout(u);const p=await this.applyResponseInterceptors(m);if(!p.ok){const v=await this.parseErrorResponse(p);throw new ol(v.message||`HTTP ${p.status}`,p.status,v)}return await p.json()}catch(f){if(clearTimeout(u),i=this.handleRequestError(f),s===this.retryAttempts||!this.isRetryableError(i))throw i;const m=this.calculateRetryDelay(s);await new Promise(p=>setTimeout(p,m))}}throw i}async parseErrorResponse(e){try{return await e.json()}catch{return{error:"Unknown Error",message:`HTTP ${e.status} ${e.statusText}`,details:{}}}}handleRequestError(e){return e.name==="AbortError"||e instanceof DOMException&&e.name==="AbortError"?new Il("Request timeout"):e instanceof TypeError&&e.message.includes("fetch")?new cl("Network error - unable to connect to API"):e instanceof ol?e:new cl(e.message||"Unknown network error")}async get(e,l={},n={}){const i=this.buildQueryString(l),o=i?`${e}?${i}`:e;return this.request(o,{method:"GET",...n})}buildQueryString(e){const l=new URLSearchParams;return Object.entries(e).forEach(([n,i])=>{i!=null&&i!==""&&l.append(n,String(i))}),l.toString()}async healthCheck(){return this.request("/health")}}const At=new pr;function gr(t){const e=t.trim().split(`
`);if(e.length<2)throw new Error("CSV file must contain at least a header row and one data row");const l=e[0].split(",").map(i=>i.trim()),n=[];for(let i=1;i<e.length;i++){const o=e[i].split(",").map(c=>c.trim());if(o.length!==l.length){console.warn(`Row ${i+1} has ${o.length} columns, expected ${l.length}. Skipping row.`);continue}const s={};l.forEach((c,u)=>{s[c]=o[u]}),n.push(s)}return n}async function br(t){try{const e=await fetch(t);if(!e.ok)throw new Error(`Failed to load CSV file: ${e.status} ${e.statusText}`);const l=await e.text();return gr(l)}catch(e){throw console.error(`Error loading CSV file ${t}:`,e),new Error(`Failed to load CSV data from ${t}: ${e.message}`)}}const Qt=new Map;async function Si(t,e=!1){if(!e&&Qt.has(t))return Qt.get(t);try{const l=await br(t);return Qt.set(t,l),l}catch(l){if(Qt.has(t))return console.warn(`Failed to reload CSV ${t}, using cached data:`,l),Qt.get(t);throw l}}async function Mi(t=!1){try{const e=await Si("/data/members.csv",t),l=["memberCode","citizenID","insurerCode","titleTH","nameTH","surnameTH"],n=e.filter(i=>{const o=l.every(s=>i[s]&&i[s].trim()!=="");return o||console.warn("Skipping member row with missing required fields:",i),o});return n.length===0?(console.warn("No valid member data found, returning fallback data"),tn()):n}catch(e){return console.error("Error loading member data:",e),tn()}}async function Di(t=!1){try{const e=await Si("/data/insurers.csv",t),l=["insurerCode","displayNameTH"];return e.filter(i=>{const o=l.every(s=>i[s]&&i[s].trim()!=="");return o||console.warn("Skipping insurer row with missing required fields:",i),o})}catch(e){return console.error("Error loading insurer data:",e),hr()}}function tn(){return[{memberCode:"MEM001",citizenID:"1234567890123",insurerCode:"INS001",titleTH:"นาย",nameTH:"สมชาย",surnameTH:"ใจดี",titleEN:"Mr.",nameEN:"Somchai",surnameEN:"Jaidee",memberStatus:"Active",memberType:"Principal",vip:"N",cardType:"Standard",language:"TH",citizenship:"Thai",countryCode:"TH"}]}function hr(){return[{insurerCode:"INS001",displayNameTH:"บริษัท ประกันภัย เอ จำกัด",displayNameEN:"Insurance Company A Ltd."}]}const ln={INSURER_CITIZEN:["INSURER_CODE","CITIZEN_ID"],INSURER_POLICY_TH:["INSURER_CODE","POLICY_NO","NAME_TH"],INSURER_POLICY_EN:["INSURER_CODE","POLICY_NO","NAME_EN"],INSURER_CERT_TH:["INSURER_CODE","CERTIFICATE_NO","NAME_TH"],INSURER_CERT_EN:["INSURER_CODE","CERTIFICATE_NO","NAME_EN"],INSURER_STAFF_TH:["INSURER_CODE","STAFF_NO","NAME_TH"],INSURER_STAFF_EN:["INSURER_CODE","STAFF_NO","NAME_EN"],INSURER_OTHER:["INSURER_CODE","OTHER_ID"],INSURER_NAME_TH:["INSURER_CODE","NAME_TH"],INSURER_NAME_EN:["INSURER_CODE","NAME_EN"]},nn={MEMBER:["MEMBER_CODE"],INSURER_CITIZEN:["INSURER_CODE","CITIZEN_ID"]};let nl=null;async function yr(){if(nl)return nl;try{const[t,e]=await Promise.all([Mi(),Di()]),l=[...new Set(t.map(c=>c.citizenID))],n=[...new Set(t.map(c=>c.memberCode))],i=[...new Set(e.map(c=>c.insurerCode))],o=[...new Set(t.map(c=>`${c.nameTH} ${c.surnameTH}`))],s=[...new Set(t.map(c=>`${c.nameEN} ${c.surnameEN}`))];return nl={INSURER_CODE:i,CITIZEN_ID:l,MEMBER_CODE:n,NAME_TH:o,NAME_EN:s,POLICY_NO:["POL001","POL002","POL003","POL004","POL005","POL006","POL007","POL008","POL009","POL010"],CERTIFICATE_NO:["CERT001","CERT002","CERT003","CERT004","CERT005","CERT006","CERT007","CERT008","CERT009","CERT010","CERT011","CERT012"],STAFF_NO:["ST001","ST002","ST003","ST004","ST005","ST006","ST007","ST008","ST009","ST010","ST011","ST012"],OTHER_ID:["EMP001","EMP002","EMP003","EMP004","EMP005","EMP006","EMP007","EMP008","EMP009","EMP010","EMP011","EMP012"]},nl}catch(t){return console.error("Error loading valid values from CSV:",t),Pi()}}function Pi(){return{INSURER_CODE:["INS001","INS002","INS003"],CITIZEN_ID:["1234567890123","1234567890124","1234567890125"],MEMBER_CODE:["MEM001","MEM002","MEM003","MEM004","MEM005","MEM006","MEM007","MEM008"],NAME_TH:["สมชาย ใจดี","มาลี สวยงาม","ปิยะดา สุขใส"],NAME_EN:["Somchai Jaidee","Malee Suayngam","Piyada Suksai"],POLICY_NO:["POL001","POL002","POL003"],CERTIFICATE_NO:["CERT001","CERT002","CERT003"],STAFF_NO:["ST001","ST002","ST003"],OTHER_ID:["EMP001","EMP002","EMP003"]}}async function jt(){return await yr()}const wt=Pi(),_r={PRINCIPAL:"Principal",DEPENDENT:"Dependent"},vr={STANDARD:"Standard",GOLD:"Gold",PLATINUM:"Platinum",DIAMOND:"Diamond"},wr={TH:"TH",EN:"EN"},Cr={YES:"Y",NO:"N"},Er={ACTIVE:"Active",INACTIVE:"Inactive",SUSPENDED:"Suspended"},Ot={async isValidInsurerCode(t){return(await jt()).INSURER_CODE.includes(t)},async isValidCitizenId(t){return(await jt()).CITIZEN_ID.includes(t)},async isValidMemberCode(t){return(await jt()).MEMBER_CODE.includes(t)},async isValidPolicyNo(t){return(await jt()).POLICY_NO.includes(t)},async isValidNameTh(t){return(await jt()).NAME_TH.includes(t)},async isValidNameEn(t){return(await jt()).NAME_EN.includes(t)},isValidMemberStatus(t){return Object.values(Er).includes(t)},isValidMemberType(t){return Object.values(_r).includes(t)},isValidVipStatus(t){return Object.values(Cr).includes(t)},isValidCardType(t){return Object.values(vr).includes(t)},isValidLanguage(t){return Object.values(wr).includes(t)},async validateMember(t){const e=[];if(!t)return{isValid:!1,errors:["Member object is required"]};const l=["memberCode","memberStatus","memberType"];for(const n of l)t[n]||e.push(`${n} is required`);try{t.memberCode&&!await this.isValidMemberCode(t.memberCode)&&e.push("Invalid member code"),t.memberStatus&&!this.isValidMemberStatus(t.memberStatus)&&e.push("Invalid member status"),t.memberType&&!this.isValidMemberType(t.memberType)&&e.push("Invalid member type"),t.vip&&!this.isValidVipStatus(t.vip)&&e.push("Invalid VIP status"),t.cardType&&!this.isValidCardType(t.cardType)&&e.push("Invalid card type"),t.language&&!this.isValidLanguage(t.language)&&e.push("Invalid language")}catch(n){console.error("Error during member validation:",n),e.push("Validation error occurred")}return{isValid:e.length===0,errors:e}}};function kr(t){const e=[],l=[],n=zl(t),i=Object.keys(n);if(i.length===0)return e.push("At least one parameter combination is required"),{isValid:!1,errors:e,warnings:l,cleanParams:n};const o=Ai(i,ln);if(!o)return e.push(`Invalid parameter combination. Must use exactly one of: ${Ri(ln)}`),{isValid:!1,errors:e,warnings:l,cleanParams:n};const s=Oi(n);return e.push(...s.errors),l.push(...s.warnings),{isValid:e.length===0,errors:e,warnings:l,cleanParams:n,combination:o}}function Tr(t){const e=[],l=[],n=zl(t),i=Object.keys(n);if(i.length===0)return e.push("At least one parameter combination is required"),{isValid:!1,errors:e,warnings:l,cleanParams:n};const o=Ai(i,nn);if(!o)return e.push(`Invalid parameter combination. Must use exactly one of: ${Ri(nn)}`),{isValid:!1,errors:e,warnings:l,cleanParams:n};const s=Oi(n);return e.push(...s.errors),l.push(...s.warnings),{isValid:e.length===0,errors:e,warnings:l,cleanParams:n,combination:o}}function Ir(t){const e=[],l=[],n=zl(t);return n.MEMBER_CODE?(Ot.isValidMemberCode(n.MEMBER_CODE)||e.push(`Invalid MEMBER_CODE: ${n.MEMBER_CODE}`),{isValid:e.length===0,errors:e,warnings:l,cleanParams:n}):(e.push("MEMBER_CODE is required"),{isValid:!1,errors:e,warnings:l,cleanParams:n})}function zl(t){const e={};return Object.entries(t||{}).forEach(([l,n])=>{n!=null&&n!==""&&(e[l]=String(n).trim())}),e}function Ai(t,e){const l=[...t].sort();for(const[n,i]of Object.entries(e)){const o=[...i].sort();if(Nr(l,o))return n}return null}function Nr(t,e){return t.length===e.length&&t.every((l,n)=>l===e[n])}function Ri(t){return Object.entries(t).map(([e,l])=>`(${l.join(" + ")})`).join(", ")}function Oi(t){const e=[],l=[];return Object.entries(t).forEach(([n,i])=>{switch(n){case"INSURER_CODE":Ot.isValidInsurerCode(i)||e.push(`Invalid INSURER_CODE: ${i}. Valid values: ${wt.INSURER_CODE.join(", ")}`);break;case"CITIZEN_ID":Ot.isValidCitizenId(i)||e.push(`Invalid CITIZEN_ID: ${i}`);break;case"MEMBER_CODE":Ot.isValidMemberCode(i)||e.push(`Invalid MEMBER_CODE: ${i}. Valid values: ${wt.MEMBER_CODE.join(", ")}`);break;case"POLICY_NO":Ot.isValidPolicyNo(i)||e.push(`Invalid POLICY_NO: ${i}. Valid values: ${wt.POLICY_NO.join(", ")}`);break;case"NAME_TH":Ot.isValidNameTh(i)||l.push(`NAME_TH "${i}" may not match exactly. Names are case-sensitive.`);break;case"NAME_EN":Ot.isValidNameEn(i)||l.push(`NAME_EN "${i}" may not match exactly. Names are case-sensitive.`);break;case"CERTIFICATE_NO":wt.CERTIFICATE_NO.includes(i)||e.push(`Invalid CERTIFICATE_NO: ${i}. Valid values: ${wt.CERTIFICATE_NO.join(", ")}`);break;case"STAFF_NO":wt.STAFF_NO.includes(i)||e.push(`Invalid STAFF_NO: ${i}. Valid values: ${wt.STAFF_NO.join(", ")}`);break;case"OTHER_ID":wt.OTHER_ID.includes(i)||e.push(`Invalid OTHER_ID: ${i}. Valid values: ${wt.OTHER_ID.join(", ")}`);break;default:l.push(`Unknown parameter: ${n}`)}}),{errors:e,warnings:l}}function xr(t,e,l){const n=new xi(t);return n.data={validationErrors:e,originalParams:l},n}function Hl(t,e){const l=e(t);if(!l.isValid)throw xr(l.errors[0]||"Validation failed",l.errors,t);return l.cleanParams}async function gl(t,e={}){try{const l=Hl(t,kr),n=await At.get("/PolicyListSF",l,e),i=n.data||n;return{success:!0,data:i,total:n.total||(Array.isArray(i)?i.length:i?1:0),params:l}}catch(l){throw It(l,{endpoint:"PolicyListSF",params:t}),l}}async function Nl(t,e={}){try{const n=Hl({MEMBER_CODE:t},Ir);return{success:!0,data:await At.get("/PolicyDetailSF",n,e),memberCode:n.MEMBER_CODE}}catch(l){throw It(l,{endpoint:"PolicyDetailSF",memberCode:t}),l}}async function Ll(t,e={}){try{const l=Hl(t,Tr),n=await At.get("/ClaimListSF",l,e),i=n.data||n;return{success:!0,data:i,total:n.total||(Array.isArray(i)?i.length:i?1:0),params:l}}catch(l){throw It(l,{endpoint:"ClaimListSF",params:t}),l}}async function Sr(t={}){try{return{success:!0,data:await At.healthCheck(t),timestamp:new Date().toISOString()}}catch(e){throw It(e,{endpoint:"health"}),e}}async function Mr(t={}){try{return{success:!0,data:await At.get("/",{},t),timestamp:new Date().toISOString()}}catch(e){throw It(e,{endpoint:"root"}),e}}async function Dr(t,e="INS001",l={}){return gl({INSURER_CODE:e,CITIZEN_ID:t},l)}async function Pr(t,e,l="INS001",n={}){const i=/[\u0E00-\u0E7F]/.test(e),o={INSURER_CODE:l,POLICY_NO:t};return i?o.NAME_TH=e:o.NAME_EN=e,gl(o,n)}async function Ar(t,e="INS001",l={}){const n=/[\u0E00-\u0E7F]/.test(t),i={INSURER_CODE:e};return n?i.NAME_TH=t:i.NAME_EN=t,gl(i,l)}async function Rr(t,e="INS001",l={}){return Ll({INSURER_CODE:e,CITIZEN_ID:t},l)}async function xl(t,e={}){return Ll({MEMBER_CODE:t},e)}async function Or(t,e={}){try{const[l,n]=await Promise.all([Nl(t,e),xl(t,e)]);return{success:!0,memberCode:t,policy:l.data,claims:n.data,timestamp:new Date().toISOString()}}catch(l){throw It(l,{endpoint:"getMemberData",memberCode:t}),l}}async function zr(t={}){try{return{success:!0,data:await At.get("/members",{},t),message:"Member list retrieved successfully",timestamp:new Date().toISOString()}}catch(e){throw It(e,{endpoint:"getAllMembersData"}),e}}async function Hr(t,e={}){try{const l={MEMBER_CODE:t};return{success:!0,data:await At.get("/member/policies",l,e),memberCode:t,message:"Member policies retrieved successfully",timestamp:new Date().toISOString()}}catch(l){throw It(l,{endpoint:"getMemberPolicies",memberCode:t}),l}}async function Lr(t,e={}){try{const l={MEMBER_CODE:t};return{success:!0,data:await At.get("/member/claims",l,e),memberCode:t,message:"Member claims retrieved successfully",timestamp:new Date().toISOString()}}catch(l){throw It(l,{endpoint:"getMemberClaims",memberCode:t}),l}}const at={policies:{search:gl,getDetail:Nl,searchByCitizenId:Dr,searchByName:Ar,searchByPolicyAndName:Pr},claims:{getList:Ll,getByCitizenId:Rr,getByMemberCode:xl},members:{getAll:zr,getData:Or,getPolicyDetail:Nl,getClaims:xl,getPolicies:Hr,getClaimsData:Lr},system:{health:Sr,info:Mr}};let vl=null,wl=null;async function zi(t=!1){return(t||!vl)&&((!wl||t)&&(wl=Mi(t)),vl=await wl),vl}function Hi(t){return{...t,memberStatus:t.memberStatus||"Active",memberType:t.memberType||"Principal",principleMemberCode:t.memberCode,principleName:`${t.nameTH} ${t.surnameTH}`,vip:t.vip||"N",vipRemarks:t.vipRemarks||"",cardType:t.cardType||"Standard",language:t.language||"TH",citizenship:t.citizenship||"Thai",countryCode:t.countryCode||"TH",birthDate:t.birthDate||null,gender:t.gender||null,mobile:t.mobile||null,email:t.email||null}}async function Vt(t=!1){try{const e=await zi();return(t?e:e.filter(n=>n.memberStatus!=="Inactive")).map(n=>Hi(n))}catch(e){return console.error("Error loading members:",e),[]}}async function Li(t){try{const l=(await zi()).find(n=>n.memberCode===t);return l?Hi(l):null}catch(e){return console.error("Error loading member by code:",e),null}}function bl(t,e="TH"){return t?t.titleTH&&t.nameTH&&t.surnameTH?`${t.titleTH}${t.nameTH} ${t.surnameTH}`:t.titleEN&&t.nameEN&&t.surnameEN?`${t.titleEN} ${t.nameEN} ${t.surnameEN}`:t.memberCode||"Unknown Member":""}function Vi(t,e="TH"){return t?t.nameTH?t.nameTH:t.nameEN?t.nameEN:t.memberCode||"Unknown":""}async function Vr(t=!1){try{return(await Vt(t)).map(l=>({value:l.memberCode,label:bl(l,"TH"),shortLabel:Vi(l,"TH"),status:l.memberStatus,vip:l.vip==="Y",cardType:l.cardType,memberType:l.memberType,language:l.language,member:l}))}catch(e){return console.error("Error loading member options:",e),[]}}async function Bi(){try{const t=await Vt(!1);return t.length>0?t[0]:null}catch(t){return console.error("Error loading default member:",t),null}}function Br(t){return t&&t.vip==="Y"}function Fr(t){return!t||!t.cardType?"":`${{Standard:"🟢",Gold:"🟡",Platinum:"⚪",Diamond:"💎"}[t.cardType]||""} ${t.cardType}`}let Cl=null,El=null;async function jr(t=!1){return(t||!Cl)&&((!El||t)&&(El=Di(t)),Cl=await El),Cl}async function Fi(t){try{const l=(await jr()).find(n=>n.insurerCode===t);return l?`${l.displayNameTH} (${l.displayNameEN})`:t||"Unknown Insurer"}catch(e){return console.error("Error loading insurer display name:",e),t||"Unknown Insurer"}}async function Ur(){try{const t=await Vt(),e=new Map;return t.forEach(l=>{e.has(l.citizenID)||e.set(l.citizenID,{citizenID:l.citizenID,displayName:`${l.titleTH} ${l.nameTH} ${l.surnameTH}`,displayNameEN:`${l.titleEN} ${l.nameEN} ${l.surnameEN}`,titleTH:l.titleTH,nameTH:l.nameTH,surnameTH:l.surnameTH,titleEN:l.titleEN,nameEN:l.nameEN,surnameEN:l.surnameEN})}),Array.from(e.values()).sort((l,n)=>l.displayName.localeCompare(n.displayName,"th"))}catch(t){return console.error("Error loading citizens:",t),[]}}async function $r(t){if(!t)return[];try{const e=await Vt(),l=new Map,n=e.filter(i=>i.citizenID===t);for(const i of n)if(!l.has(i.insurerCode)){const o=await Fi(i.insurerCode);l.set(i.insurerCode,{insurerCode:i.insurerCode,displayName:o,memberCode:i.memberCode})}return Array.from(l.values()).sort((i,o)=>i.displayName.localeCompare(o.displayName,"th"))}catch(e){return console.error("Error loading insurers for citizen:",e),[]}}async function qr(){try{const t=await Vt(),e=new Map;for(const l of t)if(!e.has(l.insurerCode)){const n=await Fi(l.insurerCode);e.set(l.insurerCode,{insurerCode:l.insurerCode,displayName:n})}return Array.from(e.values()).sort((l,n)=>l.displayName.localeCompare(n.displayName,"th"))}catch(t){return console.error("Error loading all insurers:",t),[]}}async function Yr(t,e){if(!t||!e)return null;try{return(await Vt()).find(n=>n.citizenID===t&&n.insurerCode===e)||null}catch(l){return console.error("Error finding member by citizen and insurer:",l),null}}async function Gr(){try{return(await Ur()).map(e=>({value:e.citizenID,label:e.displayName,labelEN:e.displayNameEN,citizen:e}))}catch(t){return console.error("Error loading citizen options:",t),[]}}async function Zr(t=null){try{return(t?await $r(t):await qr()).map(l=>({value:l.insurerCode,label:l.displayName,insurer:l}))}catch(e){return console.error("Error loading insurer options:",e),[]}}const fl="insurance_portal_selected_member",ji="insurance_portal_selected_citizen",Vl="insurance_portal_selected_insurer";function Jr(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(fl);return t?JSON.parse(t):null}catch(t){return console.warn("Failed to parse stored member data:",t),null}}function Ui(t){if(!(typeof window>"u"))try{t?sessionStorage.setItem(fl,JSON.stringify({memberCode:t,timestamp:Date.now()})):sessionStorage.removeItem(fl)}catch(e){console.warn("Failed to save member data to session storage:",e)}}async function Kr(){const t=Jr();if(t&&t.memberCode)try{const e=await Li(t.memberCode);if(e)return e}catch(e){console.warn("Error loading stored member:",e)}try{return await Bi()}catch(e){return console.warn("Error loading default member:",e),null}}function Wr(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(ji);if(t){const e=JSON.parse(t);return console.log(`Restored citizen selection from session: ${e.displayName} (${e.citizenID})`),e}}catch(t){console.warn("Failed to restore citizen selection from session storage:",t)}return null}function Qr(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(Vl);if(t){const e=JSON.parse(t);return console.log(`Restored insurer selection from session: ${e.displayName} (${e.insurerCode})`),e}}catch(t){console.warn("Failed to restore insurer selection from session storage:",t)}return null}const il=Lt({data:[],loading:!1,error:null,lastUpdated:null}),xe=Lt(null);Kr().then(t=>{xe.set(t)}).catch(t=>{console.error("Failed to initialize selected member:",t)});const Kt=Lt(Wr()),Wt=Lt(Qr());Tt(xe,t=>(t==null?void 0:t.memberCode)||null);const $i=Tt(xe,t=>t?bl(t,"TH"):"");Tt(xe,t=>t?Vi(t,"TH"):"");Tt(xe,t=>Br(t));Tt(xe,t=>t?Fr(t):"");const rn=Lt([]);async function Xr(){try{const t=await Vr(!1);rn.set(t)}catch(t){console.error("Error loading member options:",t),rn.set([])}}Xr();Tt(Kt,t=>(t==null?void 0:t.citizenID)||null);Tt(Wt,t=>(t==null?void 0:t.insurerCode)||null);Tt([Kt,Wt],([t,e])=>t&&e?{citizenID:t.citizenID,insurerCode:e.insurerCode,displayName:`${t.displayName} - ${e.displayName}`}:null);async function es(t=!1){const e=tl(il);if(!t&&e.data&&e.data.length>0)return e.data;il.update(l=>({...l,loading:!0,error:null}));try{await new Promise(n=>setTimeout(n,100));const l=await Vt(!1);return il.update(n=>({...n,data:l,loading:!1,error:null,lastUpdated:Date.now()})),l}catch(l){throw console.error("Failed to load member list:",l),il.update(n=>({...n,loading:!1,error:l.message||"Failed to load member list"})),l}}async function ts(t){if(!t){console.warn("Member code is required for selection");return}try{const e=await Li(t);if(!e){console.warn(`Member with code ${t} not found`);return}xe.set(e),Ui(t),console.log(`Selected member: ${bl(e,"TH")} (${t})`)}catch(e){console.error("Error selecting member:",e)}}function ls(t,e){if(!t||!e){console.warn("Citizen ID and data are required for selection");return}Kt.set(e),Wt.set(null),xe.set(null);try{typeof window<"u"&&(sessionStorage.setItem(ji,JSON.stringify(e)),sessionStorage.removeItem(Vl),sessionStorage.removeItem(fl))}catch(l){console.warn("Failed to save citizen selection to session storage:",l)}console.log(`Selected citizen: ${e.displayName} (${t})`)}async function ns(t,e){if(!t||!e){console.warn("Insurer code and data are required for selection");return}const l=tl(Kt);if(!l){console.warn("No citizen selected. Please select a citizen first.");return}Wt.set(e);try{const n=await Yr(l.citizenID,t);n?(xe.set(n),Ui(n.memberCode),console.log(`Member found and selected: ${n.memberCode}`)):(xe.set(null),console.warn(`No member found for citizen ${l.citizenID} and insurer ${t}`))}catch(n){console.error("Error finding member by citizen and insurer:",n),xe.set(null)}try{typeof window<"u"&&sessionStorage.setItem(Vl,JSON.stringify(e))}catch(n){console.warn("Failed to save insurer selection to session storage:",n)}console.log(`Selected insurer: ${e.displayName} (${t})`)}async function is(){try{if(await es(),!tl(xe)){const e=await Bi();e&&await ts(e.memberCode)}console.log("Member store initialized successfully")}catch(t){console.error("Failed to initialize member store:",t)}}typeof window<"u"&&window.location.hostname==="localhost"&&xe.subscribe(t=>{console.log("Selected member changed:",t?`${t.memberCode} - ${bl(t,"TH")}`:"None")});const rs=5*60*1e3,Sl=new Map;function qi(t,e={},l=!0){let n=[t];if(l){const o=tl(xe);o&&o.memberCode&&n.push(`member:${o.memberCode}`)}const i=Object.keys(e).sort().map(o=>`${o}:${e[o]}`).join("|");return i&&n.push(i),n.join("_")}function ss(t){return t&&Date.now()-t.timestamp<rs}function Yi(t){const e=Sl.get(t);return ss(e)?e.data:(Sl.delete(t),null)}function Gi(t,e){Sl.set(t,{data:e,timestamp:Date.now()})}function Zi(t={}){const{subscribe:e,set:l,update:n}=Lt({data:null,loading:!1,error:null,lastUpdated:null,...t});return{subscribe:e,set:l,update:n,setLoading:i=>n(o=>({...o,loading:i,error:i?null:o.error})),setData:i=>n(o=>({...o,data:i,loading:!1,error:null,lastUpdated:Date.now()})),setError:i=>n(o=>({...o,error:i,loading:!1})),reset:()=>l({data:null,loading:!1,error:null,lastUpdated:null})}}const Pt=Zi(),$t=Zi();Tt(Pt,t=>{var e;return((e=t.data)==null?void 0:e.filter(l=>l.status==="Active"))||[]});async function as(t={},e=!1){const l=tl(xe),n=qi("policies",t,!0);if(!e){const i=Yi(n);if(i)return Pt.setData(i),i}Pt.setLoading(!0);try{let i;if(t.INSURER_CODE&&t.CITIZEN_ID)i=await at.policies.searchByCitizenId(t.CITIZEN_ID,t.INSURER_CODE);else if(t.insurerCode&&t.citizenId)i=await at.policies.searchByCitizenId(t.citizenId,t.insurerCode);else if(t.CITIZEN_ID)i=await at.policies.searchByCitizenId(t.CITIZEN_ID);else if(t.citizenId)i=await at.policies.searchByCitizenId(t.citizenId);else if(t.policyNumber&&t.name)i=await at.policies.searchByPolicyAndName(t.policyNumber,t.name);else if(t.name)i=await at.policies.searchByName(t.name);else if(l&&l.citizenID&&l.insurerCode){const o=l.citizenID;i=await at.policies.searchByCitizenId(o,l.insurerCode)}else if(l&&l.citizenID){const o=l.citizenID;i=await at.policies.searchByCitizenId(o)}else i=await at.policies.searchByCitizenId("1234567890123");if(i.success&&i.data){const o=i.data;return Gi(n,o),Pt.setData(o),o}else throw new Error(i.message||"Failed to load policies")}catch(i){throw console.error("Error loading policies:",i),Pt.setError(i),i}}async function os(t,e=!1){if(!t){const n=new Error("Member code is required");throw $t.setError(n),n}const l=qi("policy_detail",{memberCode:t});if(!e){const n=Yi(l);if(n)return $t.setData(n),n}$t.setLoading(!0);try{const n=await at.members.getPolicyDetail(t);if(n.success&&n.data){const i=n.data;return Gi(l,i),$t.setData(i),i}else throw new Error(n.message||"Failed to load policy details")}catch(n){throw console.error("Error loading policy detail:",n),$t.setError(n),n}}function sn(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function an(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function cs(t,e,l){const n=t.slice();return n[4]=e[l],n[9]=l,n}function on(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function us(t,e,l){const n=t.slice();return n[4]=e[l],n[9]=l,n}function cn(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function ds(t){let e,l,n,i,o,s=t[3]&&un(t);return{c(){e=d("div"),l=te("svg"),n=te("circle"),i=te("path"),o=g(),s&&s.c(),a(n,"class","opacity-25"),a(n,"cx","12"),a(n,"cy","12"),a(n,"r","10"),a(n,"stroke","currentColor"),a(n,"stroke-width","4"),a(i,"class","opacity-75"),a(i,"fill","currentColor"),a(i,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(l,"class","animate-spin w-4 h-4"),a(l,"fill","none"),a(l,"viewBox","0 0 24 24"),a(e,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"role","status"),a(e,"aria-label","Loading")},m(c,u){k(c,e,u),r(e,l),r(l,n),r(l,i),r(e,o),s&&s.m(e,null)},p(c,u){c[3]?s?s.p(c,u):(s=un(c),s.c(),s.m(e,null)):s&&(s.d(1),s=null)},d(c){c&&E(e),s&&s.d()}}}function fs(t){let e,l,n=ue(Array(t[1])),i=[];for(let s=0;s<n.length;s+=1)i[s]=dn(sn(t,n,s));let o=t[3]&&fn(t);return{c(){e=d("div");for(let s=0;s<i.length;s+=1)i[s].c();l=g(),o&&o.c(),a(e,"class","space-y-4"),a(e,"role","status"),a(e,"aria-label","Loading list")},m(s,c){k(s,e,c);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);r(e,l),o&&o.m(e,null)},p(s,c){if(c&2){n=ue(Array(s[1]));let u;for(u=0;u<n.length;u+=1){const f=sn(s,n,u);i[u]?i[u].p(f,c):(i[u]=dn(),i[u].c(),i[u].m(e,l))}for(;u<i.length;u+=1)i[u].d(1);i.length=n.length}s[3]?o?o.p(s,c):(o=fn(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(s){s&&E(e),Ke(i,s),o&&o.d()}}}function ms(t){let e,l,n,i,o,s,c,u,f=ue(Array(3)),m=[];for(let _=0;_<f.length;_+=1)m[_]=mn(on(t,f,_));let p=ue(Array(2)),b=[];for(let _=0;_<p.length;_+=1)b[_]=pn(an(t,p,_));let v=t[3]&&gn(t);return{c(){e=d("div"),l=d("div"),l.innerHTML='<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between"><div class="flex items-center mb-4 lg:mb-0"><div class="w-12 h-12 bg-gray-200 rounded mr-4"></div> <div><div class="w-48 h-8 bg-gray-200 rounded mb-2"></div> <div class="w-32 h-5 bg-gray-200 rounded"></div></div></div> <div class="flex flex-col sm:flex-row sm:items-center gap-3"><div class="w-20 h-8 bg-gray-200 rounded-full"></div> <div class="text-right"><div class="w-24 h-8 bg-gray-200 rounded mb-1"></div> <div class="w-16 h-4 bg-gray-200 rounded"></div></div></div></div>',n=g(),i=d("div"),o=d("div");for(let _=0;_<m.length;_+=1)m[_].c();s=g(),c=d("div");for(let _=0;_<b.length;_+=1)b[_].c();u=g(),v&&v.c(),a(l,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 animate-pulse svelte-gksjlv"),a(o,"class","lg:col-span-2 xl:col-span-3 space-y-6"),a(c,"class","space-y-6"),a(i,"class","grid gap-6 grid-cols-1 lg:grid-cols-3 xl:grid-cols-4"),a(e,"role","status"),a(e,"aria-label","Loading policy details")},m(_,C){k(_,e,C),r(e,l),r(e,n),r(e,i),r(i,o);for(let h=0;h<m.length;h+=1)m[h]&&m[h].m(o,null);r(i,s),r(i,c);for(let h=0;h<b.length;h+=1)b[h]&&b[h].m(c,null);r(e,u),v&&v.m(e,null)},p(_,C){if(C&0){f=ue(Array(3));let h;for(h=0;h<f.length;h+=1){const w=on(_,f,h);m[h]?m[h].p(w,C):(m[h]=mn(w),m[h].c(),m[h].m(o,null))}for(;h<m.length;h+=1)m[h].d(1);m.length=f.length}if(C&0){p=ue(Array(2));let h;for(h=0;h<p.length;h+=1){const w=an(_,p,h);b[h]?b[h].p(w,C):(b[h]=pn(w),b[h].c(),b[h].m(c,null))}for(;h<b.length;h+=1)b[h].d(1);b.length=p.length}_[3]?v?v.p(_,C):(v=gn(_),v.c(),v.m(e,null)):v&&(v.d(1),v=null)},d(_){_&&E(e),Ke(m,_),Ke(b,_),v&&v.d()}}}function ps(t){let e,l,n=ue(Array(t[1])),i=[];for(let s=0;s<n.length;s+=1)i[s]=bn(cn(t,n,s));let o=t[3]&&hn(t);return{c(){e=d("div");for(let s=0;s<i.length;s+=1)i[s].c();l=g(),o&&o.c(),a(e,"class","grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"),a(e,"role","status"),a(e,"aria-label","Loading content")},m(s,c){k(s,e,c);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);r(e,l),o&&o.m(e,null)},p(s,c){if(c&2){n=ue(Array(s[1]));let u;for(u=0;u<n.length;u+=1){const f=cn(s,n,u);i[u]?i[u].p(f,c):(i[u]=bn(),i[u].c(),i[u].m(e,l))}for(;u<i.length;u+=1)i[u].d(1);i.length=n.length}s[3]?o?o.p(s,c):(o=hn(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(s){s&&E(e),Ke(i,s),o&&o.d()}}}function un(t){let e,l;return{c(){e=d("span"),l=y(t[2]),a(e,"class","text-sm")},m(n,i){k(n,e,i),r(e,l)},p(n,i){i&4&&T(l,n[2])},d(n){n&&E(e)}}}function dn(t){let e;return{c(){e=d("div"),e.innerHTML='<div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-8 h-8 bg-gray-200 rounded"></div> <div><div class="w-32 h-5 bg-gray-200 rounded mb-1"></div> <div class="w-24 h-4 bg-gray-200 rounded"></div></div></div> <div class="w-16 h-6 bg-gray-200 rounded-full"></div></div>',a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse svelte-gksjlv")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function fn(t){let e,l,n,i,o,s,c,u;return{c(){e=d("div"),l=d("div"),n=te("svg"),i=te("circle"),o=te("path"),s=g(),c=d("span"),u=y(t[2]),a(i,"class","opacity-25"),a(i,"cx","12"),a(i,"cy","12"),a(i,"r","10"),a(i,"stroke","currentColor"),a(i,"stroke-width","4"),a(o,"class","opacity-75"),a(o,"fill","currentColor"),a(o,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(n,"class","animate-spin w-5 h-5"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(c,"class","text-sm font-medium"),a(l,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"class","text-center py-4")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(n,i),r(n,o),r(l,s),r(l,c),r(c,u)},p(f,m){m&4&&T(u,f[2])},d(f){f&&E(e)}}}function gs(t){let e;return{c(){e=d("div"),e.innerHTML='<div class="w-24 h-4 bg-gray-200 rounded"></div> <div class="w-20 h-4 bg-gray-200 rounded"></div> ',a(e,"class","flex justify-between")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function mn(t){let e,l,n,i,o,s=ue(Array(4)),c=[];for(let u=0;u<s.length;u+=1)c[u]=gs(us(t,s,u));return{c(){e=d("div"),l=d("div"),n=g(),i=d("div");for(let u=0;u<c.length;u+=1)c[u].c();o=g(),a(l,"class","w-32 h-6 bg-gray-200 rounded mb-4"),a(i,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse svelte-gksjlv")},m(u,f){k(u,e,f),r(e,l),r(e,n),r(e,i);for(let m=0;m<c.length;m+=1)c[m]&&c[m].m(i,null);r(e,o)},p:q,d(u){u&&E(e),Ke(c,u)}}}function bs(t){let e;return{c(){e=d("div"),a(e,"class","w-full h-4 bg-gray-200 rounded")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function pn(t){let e,l,n,i,o,s=ue(Array(3)),c=[];for(let u=0;u<s.length;u+=1)c[u]=bs(cs(t,s,u));return{c(){e=d("div"),l=d("div"),n=g(),i=d("div");for(let u=0;u<c.length;u+=1)c[u].c();o=g(),a(l,"class","w-24 h-6 bg-gray-200 rounded mb-4"),a(i,"class","space-y-3"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse svelte-gksjlv")},m(u,f){k(u,e,f),r(e,l),r(e,n),r(e,i);for(let m=0;m<c.length;m+=1)c[m]&&c[m].m(i,null);r(e,o)},p:q,d(u){u&&E(e),Ke(c,u)}}}function gn(t){let e,l,n,i,o,s,c,u;return{c(){e=d("div"),l=d("div"),n=te("svg"),i=te("circle"),o=te("path"),s=g(),c=d("span"),u=y(t[2]),a(i,"class","opacity-25"),a(i,"cx","12"),a(i,"cy","12"),a(i,"r","10"),a(i,"stroke","currentColor"),a(i,"stroke-width","4"),a(o,"class","opacity-75"),a(o,"fill","currentColor"),a(o,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(n,"class","animate-spin w-5 h-5"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(c,"class","text-sm font-medium"),a(l,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"class","text-center py-8")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(n,i),r(n,o),r(l,s),r(l,c),r(c,u)},p(f,m){m&4&&T(u,f[2])},d(f){f&&E(e)}}}function bn(t){let e;return{c(){e=d("div"),e.innerHTML='<div class="flex items-center justify-between mb-3"><div class="flex items-center gap-2"><div class="w-8 h-8 bg-gray-200 rounded"></div> <div class="w-16 h-5 bg-gray-200 rounded"></div></div> <div class="w-16 h-6 bg-gray-200 rounded-full"></div></div> <div class="w-32 h-4 bg-gray-200 rounded mb-4"></div> <div class="space-y-3 mb-4"><div class="flex justify-between"><div class="w-24 h-4 bg-gray-200 rounded"></div> <div class="w-20 h-4 bg-gray-200 rounded"></div></div> <div class="flex justify-between"><div class="w-28 h-4 bg-gray-200 rounded"></div> <div class="w-16 h-4 bg-gray-200 rounded"></div></div> <div class="flex justify-between"><div class="w-16 h-4 bg-gray-200 rounded"></div> <div class="w-20 h-4 bg-gray-200 rounded"></div></div></div> <div class="border-t border-gray-100 pt-4"><div class="space-y-2"><div class="w-full h-3 bg-gray-200 rounded"></div> <div class="w-3/4 h-3 bg-gray-200 rounded"></div></div></div>',a(e,"class","bg-white rounded-lg shadow-md border border-gray-100 min-h-[280px] p-6 animate-pulse svelte-gksjlv")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function hn(t){let e,l,n,i,o,s,c,u;return{c(){e=d("div"),l=d("div"),n=te("svg"),i=te("circle"),o=te("path"),s=g(),c=d("span"),u=y(t[2]),a(i,"class","opacity-25"),a(i,"cx","12"),a(i,"cy","12"),a(i,"r","10"),a(i,"stroke","currentColor"),a(i,"stroke-width","4"),a(o,"class","opacity-75"),a(o,"fill","currentColor"),a(o,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(n,"class","animate-spin w-5 h-5"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(c,"class","text-sm font-medium"),a(l,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"class","col-span-full text-center py-4")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(n,i),r(n,o),r(l,s),r(l,c),r(c,u)},p(f,m){m&4&&T(u,f[2])},d(f){f&&E(e)}}}function hs(t){let e;function l(o,s){if(o[0]==="cards")return ps;if(o[0]==="detail")return ms;if(o[0]==="list")return fs;if(o[0]==="inline")return ds}let n=l(t),i=n&&n(t);return{c(){i&&i.c(),e=pl()},m(o,s){i&&i.m(o,s),k(o,e,s)},p(o,[s]){n===(n=l(o))&&i?i.p(o,s):(i&&i.d(1),i=n&&n(o),i&&(i.c(),i.m(e.parentNode,e)))},i:q,o:q,d(o){o&&E(e),i&&i.d(o)}}}function ys(t,e,l){let{variant:n="cards"}=e,{count:i=3}=e,{message:o="Loading..."}=e,{showMessage:s=!0}=e;return t.$$set=c=>{"variant"in c&&l(0,n=c.variant),"count"in c&&l(1,i=c.count),"message"in c&&l(2,o=c.message),"showMessage"in c&&l(3,s=c.showMessage)},[n,i,o,s]}class Ji extends Jt{constructor(e){super(),Zt(this,e,ys,hs,Ht,{variant:0,count:1,message:2,showMessage:3})}}function _s(t){let e;return{c(){e=y("Something Went Wrong")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function vs(t){let e;return{c(){e=y("Not Found")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function ws(t){let e;return{c(){e=y("Invalid Information")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function Cs(t){let e;return{c(){e=y("Service Unavailable")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function Es(t){let e;return{c(){e=y("Connection Problem")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function yn(t){let e,l,n,i,o,s,c;return{c(){e=d("button"),l=te("svg"),n=te("path"),i=g(),o=y(t[4]),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"),a(l,"class","mr-2 w-4 h-4"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(e,"class","inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(e,"aria-label","Retry the failed operation")},m(u,f){k(u,e,f),r(e,l),r(l,n),r(e,i),r(e,o),s||(c=Se(e,"click",t[7]),s=!0)},p(u,f){f&16&&T(o,u[4])},d(u){u&&E(e),s=!1,c()}}}function _n(t){let e,l,n;return{c(){e=d("button"),e.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        Go Back`,a(e,"class","inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"),a(e,"aria-label","Go back to previous page")},m(i,o){k(i,e,o),l||(n=Se(e,"click",t[10]),l=!0)},p:q,d(i){i&&E(e),l=!1,n()}}}function vn(t){let e,l=t[0]?"Hide Details":"Show Details",n,i,o;return{c(){e=d("button"),n=y(l),a(e,"class","text-gray-500 hover:text-gray-700 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded"),a(e,"aria-label","Toggle error details")},m(s,c){k(s,e,c),r(e,n),i||(o=Se(e,"click",t[8]),i=!0)},p(s,c){c&1&&l!==(l=s[0]?"Hide Details":"Show Details")&&T(n,l)},d(s){s&&E(e),i=!1,o()}}}function wn(t){let e,l,n,i,o,s,c,u=t[2].status&&Cn(t),f=t[2].code&&En(t),m=t[2].message&&t[2].message!==t[5]&&kn(t),p=t[2].timestamp&&Tn(t);return{c(){e=d("div"),l=d("h3"),l.textContent="Error Details",n=g(),i=d("div"),u&&u.c(),o=g(),f&&f.c(),s=g(),m&&m.c(),c=g(),p&&p.c(),a(l,"class","text-sm font-semibold text-gray-900 mb-2"),a(i,"class","text-left space-y-2"),a(e,"class","mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200 max-w-2xl w-full")},m(b,v){k(b,e,v),r(e,l),r(e,n),r(e,i),u&&u.m(i,null),r(i,o),f&&f.m(i,null),r(i,s),m&&m.m(i,null),r(i,c),p&&p.m(i,null)},p(b,v){b[2].status?u?u.p(b,v):(u=Cn(b),u.c(),u.m(i,o)):u&&(u.d(1),u=null),b[2].code?f?f.p(b,v):(f=En(b),f.c(),f.m(i,s)):f&&(f.d(1),f=null),b[2].message&&b[2].message!==b[5]?m?m.p(b,v):(m=kn(b),m.c(),m.m(i,c)):m&&(m.d(1),m=null),b[2].timestamp?p?p.p(b,v):(p=Tn(b),p.c(),p.m(i,null)):p&&(p.d(1),p=null)},d(b){b&&E(e),u&&u.d(),f&&f.d(),m&&m.d(),p&&p.d()}}}function Cn(t){let e,l,n,i,o=t[2].status+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="Status:",n=g(),i=d("span"),s=y(o),a(l,"class","font-medium text-gray-700"),a(i,"class","text-gray-600 font-mono"),a(e,"class","text-xs")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&4&&o!==(o=c[2].status+"")&&T(s,o)},d(c){c&&E(e)}}}function En(t){let e,l,n,i,o=t[2].code+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="Code:",n=g(),i=d("span"),s=y(o),a(l,"class","font-medium text-gray-700"),a(i,"class","text-gray-600 font-mono"),a(e,"class","text-xs")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&4&&o!==(o=c[2].code+"")&&T(s,o)},d(c){c&&E(e)}}}function kn(t){let e,l,n,i,o=t[2].message+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="Technical Message:",n=g(),i=d("span"),s=y(o),a(l,"class","font-medium text-gray-700"),a(i,"class","text-gray-600 font-mono break-words"),a(e,"class","text-xs")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&4&&o!==(o=c[2].message+"")&&T(s,o)},d(c){c&&E(e)}}}function Tn(t){let e,l,n,i,o=new Date(t[2].timestamp).toLocaleString()+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="Time:",n=g(),i=d("span"),s=y(o),a(l,"class","font-medium text-gray-700"),a(i,"class","text-gray-600 font-mono"),a(e,"class","text-xs")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&4&&o!==(o=new Date(c[2].timestamp).toLocaleString()+"")&&T(s,o)},d(c){c&&E(e)}}}function In(t){let e,l,n,i,o,s,c,u=t[3]&&Nn(t);return{c(){e=d("div"),l=te("svg"),n=te("path"),i=g(),o=d("span"),s=y(t[5]),c=g(),u&&u.c(),a(n,"fill-rule","evenodd"),a(n,"d","M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"),a(n,"clip-rule","evenodd"),a(l,"class","w-5 h-5 flex-shrink-0"),a(l,"fill","currentColor"),a(l,"viewBox","0 0 20 20"),a(o,"class","text-sm font-medium"),a(e,"class","flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800"),a(e,"role","alert")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(e,c),u&&u.m(e,null)},p(f,m){m&32&&T(s,f[5]),f[3]?u?u.p(f,m):(u=Nn(f),u.c(),u.m(e,null)):u&&(u.d(1),u=null)},d(f){f&&E(e),u&&u.d()}}}function Nn(t){let e,l,n;return{c(){e=d("button"),e.textContent="Retry",a(e,"class","ml-auto text-red-600 hover:text-red-800 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded"),a(e,"aria-label","Retry the failed operation")},m(i,o){k(i,e,o),l||(n=Se(e,"click",t[7]),l=!0)},p:q,d(i){i&&E(e),l=!1,n()}}}function xn(t){let e,l,n,i,o,s,c,u,f,m,p,b,v,_,C,h,w=t[3]&&Sn(t);return{c(){e=d("div"),l=d("div"),n=d("div"),i=d("div"),i.innerHTML='<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',o=g(),s=d("div"),c=d("p"),c.textContent="Error",u=g(),f=d("p"),m=y(t[5]),p=g(),w&&w.c(),b=g(),v=d("div"),_=d("button"),_.innerHTML='<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>',a(i,"class","flex-shrink-0"),a(c,"class","text-sm font-medium text-gray-900"),a(f,"class","mt-1 text-sm text-gray-500"),a(s,"class","ml-3 w-0 flex-1"),a(_,"class","bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"),a(_,"aria-label","Dismiss error"),a(v,"class","ml-4 flex-shrink-0 flex"),a(n,"class","flex items-start"),a(l,"class","p-4"),a(e,"class","fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-red-200 rounded-lg shadow-lg"),a(e,"role","alert")},m(S,x){k(S,e,x),r(e,l),r(l,n),r(n,i),r(n,o),r(n,s),r(s,c),r(s,u),r(s,f),r(f,m),r(s,p),w&&w.m(s,null),r(n,b),r(n,v),r(v,_),C||(h=Se(_,"click",t[11]),C=!0)},p(S,x){x&32&&T(m,S[5]),S[3]?w?w.p(S,x):(w=Sn(S),w.c(),w.m(s,null)):w&&(w.d(1),w=null)},d(S){S&&E(e),w&&w.d(),C=!1,h()}}}function Sn(t){let e,l,n,i,o;return{c(){e=d("div"),l=d("button"),n=y(t[4]),a(l,"class","text-sm font-medium text-red-600 hover:text-red-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded"),a(e,"class","mt-3")},m(s,c){k(s,e,c),r(e,l),r(l,n),i||(o=Se(l,"click",t[7]),i=!0)},p(s,c){c&16&&T(n,s[4])},d(s){s&&E(e),i=!1,o()}}}function ks(t){let e,l,n=Mn(t[1])+"",i,o,s,c,u,f,m,p,b,v,_,C,h,w;function S(P,D){return P[1]==="network"?Es:P[1]==="api"?Cs:P[1]==="validation"?ws:P[1]==="not-found"?vs:_s}let x=S(t),V=x(t),R=t[3]&&yn(t),H=t[1]==="not-found"&&_n(t),z=t[2]&&(t[2].status||t[2].stack)&&vn(t),N=t[0]&&t[2]&&wn(t),A=t[1]==="inline"&&In(t),O=t[1]==="toast"&&xn(t);return{c(){e=d("div"),l=d("div"),i=y(n),o=g(),s=d("h2"),V.c(),c=g(),u=d("p"),f=y(t[5]),m=g(),p=d("div"),R&&R.c(),b=g(),H&&H.c(),v=g(),z&&z.c(),_=g(),N&&N.c(),C=g(),A&&A.c(),h=g(),O&&O.c(),w=pl(),a(l,"class","text-6xl mb-4"),a(l,"aria-hidden","true"),a(s,"class","text-2xl font-bold text-gray-900 mb-2"),a(u,"class","text-gray-600 max-w-md mb-6 leading-relaxed"),a(p,"class","flex flex-col sm:flex-row gap-3 items-center"),a(e,"class","flex flex-col items-center justify-center py-12 px-4 text-center"),a(e,"role","alert"),a(e,"aria-live","polite")},m(P,D){k(P,e,D),r(e,l),r(l,i),r(e,o),r(e,s),V.m(s,null),r(e,c),r(e,u),r(u,f),r(e,m),r(e,p),R&&R.m(p,null),r(p,b),H&&H.m(p,null),r(p,v),z&&z.m(p,null),r(e,_),N&&N.m(e,null),k(P,C,D),A&&A.m(P,D),k(P,h,D),O&&O.m(P,D),k(P,w,D)},p(P,[D]){D&2&&n!==(n=Mn(P[1])+"")&&T(i,n),x!==(x=S(P))&&(V.d(1),V=x(P),V&&(V.c(),V.m(s,null))),D&32&&T(f,P[5]),P[3]?R?R.p(P,D):(R=yn(P),R.c(),R.m(p,b)):R&&(R.d(1),R=null),P[1]==="not-found"?H?H.p(P,D):(H=_n(P),H.c(),H.m(p,v)):H&&(H.d(1),H=null),P[2]&&(P[2].status||P[2].stack)?z?z.p(P,D):(z=vn(P),z.c(),z.m(p,null)):z&&(z.d(1),z=null),P[0]&&P[2]?N?N.p(P,D):(N=wn(P),N.c(),N.m(e,null)):N&&(N.d(1),N=null),P[1]==="inline"?A?A.p(P,D):(A=In(P),A.c(),A.m(h.parentNode,h)):A&&(A.d(1),A=null),P[1]==="toast"?O?O.p(P,D):(O=xn(P),O.c(),O.m(w.parentNode,w)):O&&(O.d(1),O=null)},i:q,o:q,d(P){P&&(E(e),E(C),E(h),E(w)),V.d(),R&&R.d(),H&&H.d(),z&&z.d(),N&&N.d(),A&&A.d(P),O&&O.d(P)}}}function Mn(t){switch(t){case"network":return"🌐";case"api":return"⚠️";case"validation":return"❌";case"not-found":return"🔍";default:return"⚠️"}}function Ts(t,e,l){let n,{variant:i="generic"}=e,{message:o=""}=e,{error:s=null}=e,{showRetry:c=!0}=e,{retryText:u="Try Again"}=e,{showDetails:f=!1}=e;const m=Al(),p={network:"Unable to connect to the server. Please check your internet connection and try again.",api:"We're experiencing technical difficulties. Please try again in a moment.",validation:"There was an issue with the provided information. Please check your input and try again.","not-found":"The requested information could not be found.",generic:"Something went wrong. Please try again."};function b(){m("retry")}function v(){l(0,f=!f)}const _=()=>m("goBack"),C=()=>m("dismiss");return t.$$set=h=>{"variant"in h&&l(1,i=h.variant),"message"in h&&l(9,o=h.message),"error"in h&&l(2,s=h.error),"showRetry"in h&&l(3,c=h.showRetry),"retryText"in h&&l(4,u=h.retryText),"showDetails"in h&&l(0,f=h.showDetails)},t.$$.update=()=>{var h;t.$$.dirty&518&&l(5,n=o||((h=s==null?void 0:s.getUserMessage)==null?void 0:h.call(s))||(s==null?void 0:s.message)||p[i]||p.generic)},[f,i,s,c,u,n,m,b,v,o,_,C]}class Bl extends Jt{constructor(e){super(),Zt(this,e,Ts,ks,Ht,{variant:1,message:9,error:2,showRetry:3,retryText:4,showDetails:0})}}function Dn(t,e,l){const n=t.slice();return n[29]=e[l],n}function Pn(t,e,l){const n=t.slice();return n[29]=e[l],n}function An(t){let e;return{c(){e=d("label"),e.textContent="เลือกสมาชิก",a(e,"for","citizen-selector"),a(e,"class","block text-sm font-medium text-gray-700 mb-1")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function Is(t){let e;return{c(){e=d("span"),e.textContent="เลือกสมาชิก",a(e,"class","text-gray-500")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function Ns(t){let e,l=t[3].displayName+"",n,i,o,s,c=t[3].citizenID+"",u,f;return{c(){e=d("span"),n=y(l),i=g(),o=d("span"),s=y("("),u=y(c),f=y(")"),a(e,"class","truncate"),a(o,"class","text-xs text-gray-500")},m(m,p){k(m,e,p),r(e,n),k(m,i,p),k(m,o,p),r(o,s),r(o,u),r(o,f)},p(m,p){p[0]&8&&l!==(l=m[3].displayName+"")&&T(n,l),p[0]&8&&c!==(c=m[3].citizenID+"")&&T(u,c)},d(m){m&&(E(e),E(i),E(o))}}}function Rn(t){let e,l,n=ue(t[8]),i=[];for(let o=0;o<n.length;o+=1)i[o]=On(Pn(t,n,o));return{c(){e=d("div"),l=d("div");for(let o=0;o<i.length;o+=1)i[o].c();a(l,"class","py-1"),a(e,"id","citizen-listbox"),a(e,"class","absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"),a(e,"role","listbox"),a(e,"aria-label","Citizen list")},m(o,s){k(o,e,s),r(e,l);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(l,null)},p(o,s){if(s[0]&2312){n=ue(o[8]);let c;for(c=0;c<n.length;c+=1){const u=Pn(o,n,c);i[c]?i[c].p(u,s):(i[c]=On(u),i[c].c(),i[c].m(l,null))}for(;c<i.length;c+=1)i[c].d(1);i.length=n.length}},d(o){o&&E(e),Ke(i,o)}}}function On(t){let e,l,n,i=t[29].label+"",o,s,c,u=t[29].value+"",f,m,p,b,v,_;function C(){return t[18](t[29])}return{c(){var h,w;e=d("button"),l=d("div"),n=d("span"),o=y(i),s=g(),c=d("span"),f=y(u),m=g(),a(n,"class","font-medium"),a(c,"class","text-xs text-gray-500"),a(l,"class","flex flex-col min-w-0 flex-1"),a(e,"type","button"),a(e,"class",p="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[29].value===((h=t[3])==null?void 0:h.citizenID)?"bg-blue-50 text-blue-700":"text-gray-900")),a(e,"role","option"),a(e,"aria-selected",b=t[29].value===((w=t[3])==null?void 0:w.citizenID))},m(h,w){k(h,e,w),r(e,l),r(l,n),r(n,o),r(l,s),r(l,c),r(c,f),r(e,m),v||(_=Se(e,"click",C),v=!0)},p(h,w){var S,x;t=h,w[0]&256&&i!==(i=t[29].label+"")&&T(o,i),w[0]&256&&u!==(u=t[29].value+"")&&T(f,u),w[0]&264&&p!==(p="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[29].value===((S=t[3])==null?void 0:S.citizenID)?"bg-blue-50 text-blue-700":"text-gray-900"))&&a(e,"class",p),w[0]&264&&b!==(b=t[29].value===((x=t[3])==null?void 0:x.citizenID))&&a(e,"aria-selected",b)},d(h){h&&E(e),v=!1,_()}}}function zn(t){let e;return{c(){e=d("label"),e.textContent="เลือกบริษัทประกัน",a(e,"for","insurer-selector"),a(e,"class","block text-sm font-medium text-gray-700 mb-1")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function xs(t){let e;return{c(){e=d("span"),e.textContent="เลือกสมาชิกก่อน",a(e,"class","text-gray-400")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function Ss(t){let e;return{c(){e=d("span"),e.textContent="เลือกบริษัทประกัน",a(e,"class","text-gray-500")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function Ms(t){let e,l=t[10].displayName+"",n,i,o,s,c=t[10].insurerCode+"",u,f;return{c(){e=d("span"),n=y(l),i=g(),o=d("span"),s=y("("),u=y(c),f=y(")"),a(e,"class","truncate"),a(o,"class","text-xs text-gray-500")},m(m,p){k(m,e,p),r(e,n),k(m,i,p),k(m,o,p),r(o,s),r(o,u),r(o,f)},p(m,p){p[0]&1024&&l!==(l=m[10].displayName+"")&&T(n,l),p[0]&1024&&c!==(c=m[10].insurerCode+"")&&T(u,c)},d(m){m&&(E(e),E(i),E(o))}}}function Hn(t){let e,l;function n(s,c){return s[9].length===0?Ps:Ds}let i=n(t),o=i(t);return{c(){e=d("div"),l=d("div"),o.c(),a(l,"class","py-1"),a(e,"id","insurer-listbox"),a(e,"class","absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"),a(e,"role","listbox"),a(e,"aria-label","Insurer list")},m(s,c){k(s,e,c),r(e,l),o.m(l,null)},p(s,c){i===(i=n(s))&&o?o.p(s,c):(o.d(1),o=i(s),o&&(o.c(),o.m(l,null)))},d(s){s&&E(e),o.d()}}}function Ds(t){let e,l=ue(t[9]),n=[];for(let i=0;i<l.length;i+=1)n[i]=Ln(Dn(t,l,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=pl()},m(i,o){for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(i,o);k(i,e,o)},p(i,o){if(o[0]&5632){l=ue(i[9]);let s;for(s=0;s<l.length;s+=1){const c=Dn(i,l,s);n[s]?n[s].p(c,o):(n[s]=Ln(c),n[s].c(),n[s].m(e.parentNode,e))}for(;s<n.length;s+=1)n[s].d(1);n.length=l.length}},d(i){i&&E(e),Ke(n,i)}}}function Ps(t){let e;return{c(){e=d("div"),e.textContent="ไม่พบบริษัทประกัน",a(e,"class","px-3 py-2 text-sm text-gray-500 text-center")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function Ln(t){let e,l,n,i=t[29].label+"",o,s,c,u=t[29].value+"",f,m,p,b,v,_;function C(){return t[20](t[29])}return{c(){var h,w;e=d("button"),l=d("div"),n=d("span"),o=y(i),s=g(),c=d("span"),f=y(u),m=g(),a(n,"class","font-medium"),a(c,"class","text-xs text-gray-500"),a(l,"class","flex flex-col min-w-0 flex-1"),a(e,"type","button"),a(e,"class",p="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[29].value===((h=t[10])==null?void 0:h.insurerCode)?"bg-blue-50 text-blue-700":"text-gray-900")),a(e,"role","option"),a(e,"aria-selected",b=t[29].value===((w=t[10])==null?void 0:w.insurerCode))},m(h,w){k(h,e,w),r(e,l),r(l,n),r(n,o),r(l,s),r(l,c),r(c,f),r(e,m),v||(_=Se(e,"click",C),v=!0)},p(h,w){var S,x;t=h,w[0]&512&&i!==(i=t[29].label+"")&&T(o,i),w[0]&512&&u!==(u=t[29].value+"")&&T(f,u),w[0]&1536&&p!==(p="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[29].value===((S=t[10])==null?void 0:S.insurerCode)?"bg-blue-50 text-blue-700":"text-gray-900"))&&a(e,"class",p),w[0]&1536&&b!==(b=t[29].value===((x=t[10])==null?void 0:x.insurerCode))&&a(e,"aria-selected",b)},d(h){h&&E(e),v=!1,_()}}}function As(t){let e,l,n,i,o,s,c,u,f,m,p,b,v,_,C,h,w,S,x,V,R,H,z,N,A,O,P,D,I,M,F,Y=t[1]&&An();function X(L,U){return L[3]?Ns:Is}let K=X(t),J=K(t),W=t[4]&&Rn(t),ne=t[1]&&zn();function de(L,U){return L[10]?Ms:L[3]?Ss:xs}let Q=de(t),ie=Q(t),$=t[5]&&t[3]&&Hn(t);return{c(){var L,U;e=d("div"),l=d("div"),Y&&Y.c(),n=g(),i=d("div"),o=d("button"),s=d("div"),J.c(),c=g(),u=te("svg"),f=te("path"),b=g(),W&&W.c(),_=g(),C=d("div"),ne&&ne.c(),h=g(),w=d("div"),S=d("button"),x=d("div"),ie.c(),V=g(),R=te("svg"),H=te("path"),P=g(),$&&$.c(),a(s,"class","flex items-center space-x-2 min-w-0 flex-1"),a(f,"stroke-linecap","round"),a(f,"stroke-linejoin","round"),a(f,"stroke-width","2"),a(f,"d","M19 9l-7 7-7-7"),a(u,"class",m="w-4 h-4 text-gray-400 transition-transform duration-200 "+(t[4]?"transform rotate-180":"")),a(u,"fill","none"),a(u,"stroke","currentColor"),a(u,"viewBox","0 0 24 24"),a(u,"aria-hidden","true"),a(o,"id","citizen-selector"),a(o,"type","button"),a(o,"class","inline-flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"),a(o,"aria-label",p="Select citizen: "+(((L=t[3])==null?void 0:L.displayName)||"ไม่ได้เลือกสมาชิก")),a(i,"class","relative"),a(i,"role","combobox"),a(i,"aria-expanded",t[4]),a(i,"aria-haspopup","listbox"),a(i,"aria-controls","citizen-listbox"),a(i,"aria-label","Select citizen"),a(l,"class",v="relative "+(t[2]?"flex-1 min-w-0":"")),a(x,"class","flex items-center space-x-2 min-w-0 flex-1"),a(H,"stroke-linecap","round"),a(H,"stroke-linejoin","round"),a(H,"stroke-width","2"),a(H,"d","M19 9l-7 7-7-7"),a(R,"class",z="w-4 h-4 text-gray-400 transition-transform duration-200 "+(t[5]?"transform rotate-180":"")),a(R,"fill","none"),a(R,"stroke","currentColor"),a(R,"viewBox","0 0 24 24"),a(R,"aria-hidden","true"),a(S,"id","insurer-selector"),a(S,"type","button"),a(S,"class",N="inline-flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 "+(t[3]?"":"opacity-50 cursor-not-allowed")),S.disabled=A=!t[3],a(S,"aria-label",O="Select insurer: "+(((U=t[10])==null?void 0:U.displayName)||"ไม่ได้เลือกบริษัทประกัน")),a(w,"class","relative"),a(w,"role","combobox"),a(w,"aria-expanded",t[5]),a(w,"aria-haspopup","listbox"),a(w,"aria-controls","insurer-listbox"),a(w,"aria-label","Select insurer"),a(C,"class",D="relative "+(t[2]?"flex-1 min-w-0":"")),a(e,"class",I=(t[2]?"flex flex-col sm:flex-row sm:space-x-2 sm:space-y-0 space-y-3":"flex flex-col space-y-3")+" "+(t[0]?"min-w-[200px]":"min-w-[280px]"))},m(L,U){k(L,e,U),r(e,l),Y&&Y.m(l,null),r(l,n),r(l,i),r(i,o),r(o,s),J.m(s,null),r(o,c),r(o,u),r(u,f),r(i,b),W&&W.m(i,null),t[19](i),r(e,_),r(e,C),ne&&ne.m(C,null),r(C,h),r(C,w),r(w,S),r(S,x),ie.m(x,null),r(S,V),r(S,R),r(R,H),r(w,P),$&&$.m(w,null),t[21](w),M||(F=[Se(o,"click",t[13]),Se(S,"click",t[14])],M=!0)},p(L,U){var Te,ge;L[1]?Y||(Y=An(),Y.c(),Y.m(l,n)):Y&&(Y.d(1),Y=null),K===(K=X(L))&&J?J.p(L,U):(J.d(1),J=K(L),J&&(J.c(),J.m(s,null))),U[0]&16&&m!==(m="w-4 h-4 text-gray-400 transition-transform duration-200 "+(L[4]?"transform rotate-180":""))&&a(u,"class",m),U[0]&8&&p!==(p="Select citizen: "+(((Te=L[3])==null?void 0:Te.displayName)||"ไม่ได้เลือกสมาชิก"))&&a(o,"aria-label",p),L[4]?W?W.p(L,U):(W=Rn(L),W.c(),W.m(i,null)):W&&(W.d(1),W=null),U[0]&16&&a(i,"aria-expanded",L[4]),U[0]&4&&v!==(v="relative "+(L[2]?"flex-1 min-w-0":""))&&a(l,"class",v),L[1]?ne||(ne=zn(),ne.c(),ne.m(C,h)):ne&&(ne.d(1),ne=null),Q===(Q=de(L))&&ie?ie.p(L,U):(ie.d(1),ie=Q(L),ie&&(ie.c(),ie.m(x,null))),U[0]&32&&z!==(z="w-4 h-4 text-gray-400 transition-transform duration-200 "+(L[5]?"transform rotate-180":""))&&a(R,"class",z),U[0]&8&&N!==(N="inline-flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 "+(L[3]?"":"opacity-50 cursor-not-allowed"))&&a(S,"class",N),U[0]&8&&A!==(A=!L[3])&&(S.disabled=A),U[0]&1024&&O!==(O="Select insurer: "+(((ge=L[10])==null?void 0:ge.displayName)||"ไม่ได้เลือกบริษัทประกัน"))&&a(S,"aria-label",O),L[5]&&L[3]?$?$.p(L,U):($=Hn(L),$.c(),$.m(w,null)):$&&($.d(1),$=null),U[0]&32&&a(w,"aria-expanded",L[5]),U[0]&4&&D!==(D="relative "+(L[2]?"flex-1 min-w-0":""))&&a(C,"class",D),U[0]&5&&I!==(I=(L[2]?"flex flex-col sm:flex-row sm:space-x-2 sm:space-y-0 space-y-3":"flex flex-col space-y-3")+" "+(L[0]?"min-w-[200px]":"min-w-[280px]"))&&a(e,"class",I)},i:q,o:q,d(L){L&&E(e),Y&&Y.d(),J.d(),W&&W.d(),t[19](null),ne&&ne.d(),ie.d(),$&&$.d(),t[21](null),M=!1,Et(F)}}}function Rs(t,e,l){let n,i,o,s,c;ot(t,xe,I=>l(15,o=I)),ot(t,Wt,I=>l(16,s=I)),ot(t,Kt,I=>l(17,c=I));let{compact:u=!1}=e,{showLabels:f=!0}=e,{horizontal:m=!1}=e,p=!1,b=!1,v=null,_=null,C=[],h=[];ul(async()=>{try{l(8,C=await Gr())}catch(I){console.error("Error loading citizen options:",I),l(8,C=[])}finally{}});async function w(I){try{l(9,h=await Zr(I))}catch(M){console.error("Error loading insurer options:",M),l(9,h=[])}finally{}}function S(I){ls(I.value,I.citizen),R()}function x(I){ns(I.value,I.insurer),z()}function V(){l(4,p=!0),l(5,b=!1)}function R(){l(4,p=!1)}function H(){n&&(l(5,b=!0),l(4,p=!1))}function z(){l(5,b=!1)}function N(I){v&&!v.contains(I.target)&&R(),_&&!_.contains(I.target)&&z()}ul(()=>(document.addEventListener("click",N),()=>{document.removeEventListener("click",N)}));const A=I=>S(I);function O(I){dl[I?"unshift":"push"](()=>{v=I,l(6,v)})}const P=I=>x(I);function D(I){dl[I?"unshift":"push"](()=>{_=I,l(7,_)})}return t.$$set=I=>{"compact"in I&&l(0,u=I.compact),"showLabels"in I&&l(1,f=I.showLabels),"horizontal"in I&&l(2,m=I.horizontal)},t.$$.update=()=>{t.$$.dirty[0]&131072&&l(3,n=c),t.$$.dirty[0]&65536&&l(10,i=s),t.$$.dirty[0]&32768,t.$$.dirty[0]&8&&(n?w(n.citizenID):l(9,h=[]))},[u,f,m,n,p,b,v,_,C,h,i,S,x,V,H,o,s,c,A,O,P,D]}class Os extends Jt{constructor(e){super(),Zt(this,e,Rs,As,Ht,{compact:0,showLabels:1,horizontal:2},null,[-1,-1])}}function Vn(t,e,l){const n=t.slice();return n[21]=e[l],n}function zs(t){let e;return{c(){e=d("p"),e.textContent="กำลังโหลดข้อมูลสมาชิก...",a(e,"class","text-gray-600")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function Hs(t){return{c:q,m:q,d:q}}function Ls(t){var $e,We,yt,dt,ft,nt,Qe,it,_t;let e,l,n,i,o,s,c,u,f=((($e=t[0])==null?void 0:$e.titleTH)||"")+"",m,p,b=(((We=t[0])==null?void 0:We.nameTH)||"")+"",v,_,C=(((yt=t[0])==null?void 0:yt.surnameTH)||"")+"",h,w,S,x,V,R,H=(((dt=t[0])==null?void 0:dt.titleEN)||"")+"",z,N,A=(((ft=t[0])==null?void 0:ft.nameEN)||"")+"",O,P,D=(((nt=t[0])==null?void 0:nt.surnameEN)||"")+"",I,M,F,Y,X,K,J=(((Qe=t[0])==null?void 0:Qe.citizenID)||"ไม่ระบุ")+"",W,ne,de,Q,ie,$,L=qt((it=t[7][0])==null?void 0:it.BirthDate)+"",U,Te,ge,Fe,fe,re,se=t[7][0].Gender==="M"?"ชาย":t[7][0].Gender==="F"?"หญิง":"ไม่ระบุ",me,oe,le,ae,B,ee,qe=(t[7][0].Citizenship==="Thai"?"ไทย":t[7][0].Citizenship)+"",ut,gt,Le,ve,Nt,we,je,lt,bt,Ie,ye=[],ht=new Map,he=t[7][0].CompanyName&&t[7][0].CompanyName!==t[7][0].InsurerName&&Bn(t),ce=((_t=t[0])==null?void 0:_t.memberCode)&&Fn(t),Ye=ue(t[7]);const xt=j=>j[21].MemberCode||j[21].PolicyNo;for(let j=0;j<Ye.length;j+=1){let Z=Vn(t,Ye,j),be=xt(Z);ht.set(be,ye[j]=Jn(be,Z))}return{c(){e=d("section"),l=d("h2"),l.textContent="ข้อมูลสมาชิก",n=g(),i=d("div"),o=d("div"),s=d("div"),s.textContent="ชื่อ-นามสกุล (ไทย)",c=g(),u=d("p"),m=y(f),p=g(),v=y(b),_=g(),h=y(C),w=g(),S=d("div"),x=d("div"),x.textContent="ชื่อ-นามสกุล (อังกฤษ)",V=g(),R=d("p"),z=y(H),N=g(),O=y(A),P=g(),I=y(D),M=g(),F=d("div"),Y=d("div"),Y.textContent="เลขบัตรประชาชน",X=g(),K=d("p"),W=y(J),ne=g(),de=d("div"),Q=d("div"),Q.textContent="วันเกิด",ie=g(),$=d("p"),U=y(L),Te=g(),ge=d("div"),Fe=d("div"),Fe.textContent="เพศ",fe=g(),re=d("p"),me=y(se),oe=g(),le=d("div"),ae=d("div"),ae.textContent="สัญชาติ",B=g(),ee=d("p"),ut=y(qe),gt=g(),he&&he.c(),Le=g(),ve=d("p"),Nt=y("กรมธรรม์ของ "),we=d("span"),je=y(t[4]),lt=g(),ce&&ce.c(),bt=g(),Ie=d("section");for(let j=0;j<ye.length;j+=1)ye[j].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(s,"class","text-sm font-medium text-gray-500 mb-1"),a(u,"class","text-gray-900"),a(x,"class","text-sm font-medium text-gray-500 mb-1"),a(R,"class","text-gray-900"),a(Y,"class","text-sm font-medium text-gray-500 mb-1"),a(K,"class","text-gray-900 font-mono"),a(Q,"class","text-sm font-medium text-gray-500 mb-1"),a($,"class","text-gray-900"),a(Fe,"class","text-sm font-medium text-gray-500 mb-1"),a(re,"class","text-gray-900"),a(ae,"class","text-sm font-medium text-gray-500 mb-1"),a(ee,"class","text-gray-900"),a(i,"class","grid gap-4 sm:grid-cols-2"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(we,"class","font-semibold text-gray-800"),a(ve,"class","pt-10 text-gray-600"),a(Ie,"class","grid gap-6 py-6 grid-cols-1 sm:grid-cols-1 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4"),a(Ie,"aria-label","Policy cards grid")},m(j,Z){k(j,e,Z),r(e,l),r(e,n),r(e,i),r(i,o),r(o,s),r(o,c),r(o,u),r(u,m),r(u,p),r(u,v),r(u,_),r(u,h),r(i,w),r(i,S),r(S,x),r(S,V),r(S,R),r(R,z),r(R,N),r(R,O),r(R,P),r(R,I),r(i,M),r(i,F),r(F,Y),r(F,X),r(F,K),r(K,W),r(i,ne),r(i,de),r(de,Q),r(de,ie),r(de,$),r($,U),r(i,Te),r(i,ge),r(ge,Fe),r(ge,fe),r(ge,re),r(re,me),r(i,oe),r(i,le),r(le,ae),r(le,B),r(le,ee),r(ee,ut),r(i,gt),he&&he.m(i,null),k(j,Le,Z),k(j,ve,Z),r(ve,Nt),r(ve,we),r(we,je),r(ve,lt),ce&&ce.m(ve,null),k(j,bt,Z),k(j,Ie,Z);for(let be=0;be<ye.length;be+=1)ye[be]&&ye[be].m(Ie,null)},p(j,Z){var be,Ge,Ze,Xe,rt,St,et,st,vt;Z&1&&f!==(f=(((be=j[0])==null?void 0:be.titleTH)||"")+"")&&T(m,f),Z&1&&b!==(b=(((Ge=j[0])==null?void 0:Ge.nameTH)||"")+"")&&T(v,b),Z&1&&C!==(C=(((Ze=j[0])==null?void 0:Ze.surnameTH)||"")+"")&&T(h,C),Z&1&&H!==(H=(((Xe=j[0])==null?void 0:Xe.titleEN)||"")+"")&&T(z,H),Z&1&&A!==(A=(((rt=j[0])==null?void 0:rt.nameEN)||"")+"")&&T(O,A),Z&1&&D!==(D=(((St=j[0])==null?void 0:St.surnameEN)||"")+"")&&T(I,D),Z&1&&J!==(J=(((et=j[0])==null?void 0:et.citizenID)||"ไม่ระบุ")+"")&&T(W,J),Z&128&&L!==(L=qt((st=j[7][0])==null?void 0:st.BirthDate)+"")&&T(U,L),Z&128&&se!==(se=j[7][0].Gender==="M"?"ชาย":j[7][0].Gender==="F"?"หญิง":"ไม่ระบุ")&&T(me,se),Z&128&&qe!==(qe=(j[7][0].Citizenship==="Thai"?"ไทย":j[7][0].Citizenship)+"")&&T(ut,qe),j[7][0].CompanyName&&j[7][0].CompanyName!==j[7][0].InsurerName?he?he.p(j,Z):(he=Bn(j),he.c(),he.m(i,null)):he&&(he.d(1),he=null),Z&16&&T(je,j[4]),(vt=j[0])!=null&&vt.memberCode?ce?ce.p(j,Z):(ce=Fn(j),ce.c(),ce.m(ve,null)):ce&&(ce.d(1),ce=null),Z&1920&&(Ye=ue(j[7]),ye=ar(ye,Z,xt,1,j,Ye,ht,Ie,sr,Jn,null,Vn))},i:q,o:q,d(j){j&&(E(e),E(Le),E(ve),E(bt),E(Ie)),he&&he.d(),ce&&ce.d();for(let Z=0;Z<ye.length;Z+=1)ye[Z].d()}}}function Vs(t){let e,l,n,i,o,s,c,u,f,m;return{c(){e=d("div"),l=d("div"),l.textContent="📄",n=g(),i=d("h2"),i.textContent="ไม่พบข้อมูลกรมธรรม์",o=g(),s=d("p"),s.textContent="ไม่พบกรมธรรม์ประกันภัยสำหรับสมาชิกท่านนี้",c=g(),u=d("button"),u.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>
          รีเฟรช`,a(l,"class","text-6xl mb-4"),a(l,"aria-hidden","true"),a(i,"class","text-2xl font-bold text-gray-900 mb-2"),a(s,"class","text-gray-600 mb-6"),a(u,"class","inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(u,"aria-label","Refresh policies"),a(e,"class","text-center py-12")},m(p,b){k(p,e,b),r(e,l),r(e,n),r(e,i),r(e,o),r(e,s),r(e,c),r(e,u),f||(m=Se(u,"click",t[11]),f=!0)},p:q,i:q,o:q,d(p){p&&E(e),f=!1,m()}}}function Bs(t){let e,l;return e=new Bl({props:{variant:"api",error:t[5],message:"ไม่สามารถโหลดข้อมูลกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"}}),e.$on("retry",t[11]),{c(){kt(e.$$.fragment)},m(n,i){mt(e,n,i),l=!0},p(n,i){const o={};i&32&&(o.error=n[5]),e.$set(o)},i(n){l||(He(e.$$.fragment,n),l=!0)},o(n){Ue(e.$$.fragment,n),l=!1},d(n){pt(e,n)}}}function Fs(t){let e,l;return e=new Ji({props:{variant:"cards",count:6,message:"กำลังโหลดข้อมูลกรมธรรม์..."}}),{c(){kt(e.$$.fragment)},m(n,i){mt(e,n,i),l=!0},p:q,i(n){l||(He(e.$$.fragment,n),l=!0)},o(n){Ue(e.$$.fragment,n),l=!1},d(n){pt(e,n)}}}function js(t){let e;return{c(){e=d("div"),e.innerHTML='<div class="text-6xl mb-4" aria-hidden="true">👤</div> <h2 class="text-2xl font-bold text-gray-900 mb-2">กรุณาเลือกข้อมูลสมาชิก</h2> <p class="text-gray-600 mb-6">กรุณาเลือกสมาชิกและบริษัทประกันจากด้านบนเพื่อดูข้อมูลกรมธรรม์</p>',a(e,"class","text-center py-12")},m(l,n){k(l,e,n)},p:q,i:q,o:q,d(l){l&&E(e)}}}function Bn(t){let e,l,n,i,o=(t[7][0].CompanyName||t[7][0].CompanyNameEN)+"",s;return{c(){e=d("div"),l=d("div"),l.textContent="บริษัทคู่สัญญา",n=g(),i=d("p"),s=y(o),a(l,"class","text-sm font-medium text-gray-500 mb-1"),a(i,"class","text-gray-900")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&128&&o!==(o=(c[7][0].CompanyName||c[7][0].CompanyNameEN)+"")&&T(s,o)},d(c){c&&E(e)}}}function Fn(t){let e,l,n=t[0].memberCode+"",i,o;return{c(){e=d("span"),l=y("(รหัส: "),i=y(n),o=y(")"),a(e,"class","text-sm text-gray-500 ml-2")},m(s,c){k(s,e,c),r(e,l),r(e,i),r(e,o)},p(s,c){c&1&&n!==(n=s[0].memberCode+"")&&T(i,n)},d(s){s&&E(e)}}}function jn(t){let e;return{c(){e=d("span"),e.textContent="VIP",a(e,"class","px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function Un(t){let e,l,n,i,o=t[21].InsurerCardNo+"",s;return{c(){e=d("p"),l=d("span"),l.textContent="เลขบัตรประกัน",n=g(),i=d("span"),s=y(o),a(l,"class","text-sm text-gray-500"),a(i,"class","text-sm font-medium text-gray-900 text-right"),a(e,"class","text-sm text-gray-600 flex justify-between items-center")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&128&&o!==(o=c[21].InsurerCardNo+"")&&T(s,o)},d(c){c&&E(e)}}}function $n(t){let e,l,n,i,o=t[21].StaffNo+"",s;return{c(){e=d("p"),l=d("span"),l.textContent="รหัสพนักงานตัวแทน",n=g(),i=d("span"),s=y(o),a(l,"class","text-sm text-gray-500"),a(i,"class","text-sm font-medium text-gray-900 text-right"),a(e,"class","text-sm text-gray-600 flex justify-between items-center")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&128&&o!==(o=c[21].StaffNo+"")&&T(s,o)},d(c){c&&E(e)}}}function qn(t){let e,l,n,i,o,s=t[21].CoverageAmount&&Yn(t),c=t[21].Premium&&Gn(t);return{c(){e=d("div"),l=d("h3"),l.textContent="ความคุ้มครอง",n=g(),i=d("div"),s&&s.c(),o=g(),c&&c.c(),a(l,"class","text-xs font-semibold text-blue-700 uppercase tracking-wide"),a(i,"class","space-y-2 text-xs"),a(e,"class","bg-blue-50 rounded-lg p-3 space-y-2")},m(u,f){k(u,e,f),r(e,l),r(e,n),r(e,i),s&&s.m(i,null),r(i,o),c&&c.m(i,null)},p(u,f){u[21].CoverageAmount?s?s.p(u,f):(s=Yn(u),s.c(),s.m(i,o)):s&&(s.d(1),s=null),u[21].Premium?c?c.p(u,f):(c=Gn(u),c.c(),c.m(i,null)):c&&(c.d(1),c=null)},d(u){u&&E(e),s&&s.d(),c&&c.d()}}}function Yn(t){let e,l,n,i,o=ml(t[21].CoverageAmount)+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="วงเงินคุ้มครอง",n=g(),i=d("span"),s=y(o),a(l,"class","text-blue-600"),a(i,"class","font-semibold text-blue-900"),a(e,"class","flex justify-between items-center")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&128&&o!==(o=ml(c[21].CoverageAmount)+"")&&T(s,o)},d(c){c&&E(e)}}}function Gn(t){let e,l,n,i,o=ml(t[21].Premium)+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="เบี้ยประกัน",n=g(),i=d("span"),s=y(o),a(l,"class","text-blue-600"),a(i,"class","font-semibold text-blue-900"),a(e,"class","flex justify-between items-center")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&128&&o!==(o=ml(c[21].Premium)+"")&&T(s,o)},d(c){c&&E(e)}}}function Zn(t){let e,l,n,i,o=t[21].PlanCode+"",s;return{c(){e=d("div"),l=d("span"),l.textContent="รหัสแผน:",n=g(),i=d("span"),s=y(o),a(l,"class","text-green-600"),a(i,"class","font-medium text-green-900"),a(e,"class","flex justify-between items-center")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i),r(i,s)},p(c,u){u&128&&o!==(o=c[21].PlanCode+"")&&T(s,o)},d(c){c&&E(e)}}}function Jn(t,e){let l,n,i,o,s,c=e[9](e[21].PlanName)+"",u,f,m=Ml(e[21].PlanName)+"",p,b,v,_,C,h=e[21].MemberStatus+"",w,S,x,V,R,H,z,N,A,O,P=e[21].PolicyNo+"",D,I,M,F,Y,X,K=e[21].CertificateNo+"",J,W,ne,de,Q,ie,$,L,U,Te,ge,Fe,fe,re=e[21].MemberType+"",se,me,oe,le,ae,B,ee=e[21].CardType+"",qe,ut,gt,Le,ve,Nt,we,je,lt,bt,Ie,ye=qt(e[21].PlanEffFrom)+"",ht,he,ce,Ye,xt,$e,We=qt(e[21].PlanEffTo)+"",yt,dt,ft,nt,Qe,it,_t,j,Z=(e[21].InsurerName||e[21].InsurerNameEN)+"",be,Ge,Ze,Xe,rt,St,et,st=e[21].PlanCode+"",vt,Me,_e,De,Ce,Ee=e[21].PlanName+"",Ne,Pe,Rt,Mt,Je,Ve=e[21].VIP==="Y"&&jn(),Ae=e[21].InsurerCardNo&&Un(e),Re=e[21].StaffNo&&$n(e),Oe=(e[21].CoverageAmount||e[21].Premium)&&qn(e),ze=e[21].PlanCode&&Zn(e);function ll(){return e[17](e[21])}return{key:t,first:null,c(){l=d("button"),n=d("div"),i=d("div"),o=d("h2"),s=d("span"),u=y(c),f=g(),p=y(m),v=g(),_=d("div"),C=d("span"),w=y(h),V=g(),Ve&&Ve.c(),R=g(),H=d("div"),z=d("p"),N=d("span"),N.textContent="เลขที่กรมธรรม์",A=g(),O=d("span"),D=y(P),I=g(),M=d("p"),F=d("span"),F.textContent="เลขที่ใบรับรอง",Y=g(),X=d("span"),J=y(K),W=g(),Ae&&Ae.c(),ne=g(),Re&&Re.c(),de=g(),Q=d("div"),ie=d("div"),$=d("h3"),$.textContent="ข้อมูลสมาชิก",L=g(),U=d("div"),Te=d("div"),ge=d("span"),ge.textContent="ประเภทสมาชิก",Fe=g(),fe=d("div"),se=y(re),me=g(),oe=d("div"),le=d("span"),le.textContent="ประเภทบัตร",ae=g(),B=d("div"),qe=y(ee),ut=g(),Oe&&Oe.c(),gt=g(),Le=d("div"),ve=d("h3"),ve.textContent="ระยะเวลาคุ้มครอง",Nt=g(),we=d("div"),je=d("div"),lt=d("span"),lt.textContent="วันที่เริ่มต้น",bt=g(),Ie=d("span"),ht=y(ye),he=g(),ce=d("div"),Ye=d("span"),Ye.textContent="วันที่สิ้นสุด",xt=g(),$e=d("span"),yt=y(We),dt=g(),ze&&ze.c(),ft=g(),nt=d("div"),Qe=d("div"),it=d("span"),it.textContent="บริษัทประกัน",_t=g(),j=d("span"),be=y(Z),Ge=g(),Ze=d("div"),Xe=d("div"),rt=d("p"),St=y(`รหัสแผน:
                  `),et=d("span"),vt=y(st),Me=g(),_e=d("p"),De=y(`ชื่อแผน:
                  `),Ce=d("span"),Ne=y(Ee),Pe=g(),a(s,"class","text-2xl"),a(s,"aria-hidden","true"),a(o,"id",b="policy-"+e[21].MemberCode+"-title"),a(o,"class","text-lg font-semibold text-gray-900 flex items-center gap-2"),a(C,"class",S="px-3 py-1 rounded-full text-xs font-medium border "+(e[8][e[21].MemberStatus]||e[8].Active)),a(C,"aria-label",x="Member status: "+e[21].MemberStatus),a(_,"class","flex flex-row items-end gap-1"),a(i,"class","flex items-center justify-between mb-3"),a(N,"class","text-sm text-gray-500"),a(O,"class","text-sm font-medium text-gray-900 text-right"),a(z,"class","text-sm text-gray-600 flex justify-between items-center"),a(F,"class","text-sm text-gray-500"),a(X,"class","text-sm font-medium text-gray-900 text-right"),a(M,"class","text-sm text-gray-600 flex justify-between items-center"),a(H,"class","space-y-1 mb-3"),a(n,"class","mb-4"),a($,"class","text-xs font-semibold text-gray-700 uppercase tracking-wide"),a(ge,"class","text-gray-500"),a(fe,"class","font-medium text-gray-900"),a(le,"class","text-gray-500"),a(B,"class","font-medium text-gray-900"),a(U,"class","grid grid-cols-2 gap-2 text-xs"),a(ie,"class","bg-gray-50 rounded-lg p-3 space-y-2"),a(ve,"class","text-xs font-semibold text-green-700 uppercase tracking-wide"),a(lt,"class","text-green-600"),a(Ie,"class","font-medium text-green-900"),a(je,"class","flex justify-between items-center"),a(Ye,"class","text-green-600"),a($e,"class","font-medium text-green-900"),a(ce,"class","flex justify-between items-center"),a(we,"class","space-y-2 text-xs"),a(Le,"class","bg-green-50 rounded-lg p-3 space-y-2"),a(it,"class","text-sm text-gray-500"),a(j,"class","text-sm font-medium text-gray-900 text-right"),a(Qe,"class","flex justify-between items-center"),a(nt,"class","space-y-2"),a(Q,"class","space-y-4 mb-4 flex-grow"),a(et,"class","bg-gray-100 text-gray-700 px-2 py-1 rounded"),a(rt,"class","text-xs text-gray-600 leading-relaxed line-clamp-2"),a(Ce,"class","bg-gray-100 text-gray-700 px-2 py-1 rounded"),a(_e,"class","text-xs text-gray-600 leading-relaxed line-clamp-2"),a(Xe,"class","space-y-2"),a(Ze,"class","border-t border-gray-100 pt-4"),a(l,"class","bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out hover:scale-102 transform border border-gray-100 min-h-[480px] p-6 flex flex-col justify-between cursor-pointer text-left w-full"),a(l,"aria-labelledby",Rt="policy-"+e[21].MemberCode+"-title"),this.first=l},m(Dt,pe){k(Dt,l,pe),r(l,n),r(n,i),r(i,o),r(o,s),r(s,u),r(o,f),r(o,p),r(i,v),r(i,_),r(_,C),r(C,w),r(_,V),Ve&&Ve.m(_,null),r(n,R),r(n,H),r(H,z),r(z,N),r(z,A),r(z,O),r(O,D),r(H,I),r(H,M),r(M,F),r(M,Y),r(M,X),r(X,J),r(H,W),Ae&&Ae.m(H,null),r(H,ne),Re&&Re.m(H,null),r(l,de),r(l,Q),r(Q,ie),r(ie,$),r(ie,L),r(ie,U),r(U,Te),r(Te,ge),r(Te,Fe),r(Te,fe),r(fe,se),r(U,me),r(U,oe),r(oe,le),r(oe,ae),r(oe,B),r(B,qe),r(Q,ut),Oe&&Oe.m(Q,null),r(Q,gt),r(Q,Le),r(Le,ve),r(Le,Nt),r(Le,we),r(we,je),r(je,lt),r(je,bt),r(je,Ie),r(Ie,ht),r(we,he),r(we,ce),r(ce,Ye),r(ce,xt),r(ce,$e),r($e,yt),r(we,dt),ze&&ze.m(we,null),r(Q,ft),r(Q,nt),r(nt,Qe),r(Qe,it),r(Qe,_t),r(Qe,j),r(j,be),r(l,Ge),r(l,Ze),r(Ze,Xe),r(Xe,rt),r(rt,St),r(rt,et),r(et,vt),r(Xe,Me),r(Xe,_e),r(_e,De),r(_e,Ce),r(Ce,Ne),r(l,Pe),Mt||(Je=Se(l,"click",ll),Mt=!0)},p(Dt,pe){e=Dt,pe&128&&c!==(c=e[9](e[21].PlanName)+"")&&T(u,c),pe&128&&m!==(m=Ml(e[21].PlanName)+"")&&T(p,m),pe&128&&b!==(b="policy-"+e[21].MemberCode+"-title")&&a(o,"id",b),pe&128&&h!==(h=e[21].MemberStatus+"")&&T(w,h),pe&128&&S!==(S="px-3 py-1 rounded-full text-xs font-medium border "+(e[8][e[21].MemberStatus]||e[8].Active))&&a(C,"class",S),pe&128&&x!==(x="Member status: "+e[21].MemberStatus)&&a(C,"aria-label",x),e[21].VIP==="Y"?Ve||(Ve=jn(),Ve.c(),Ve.m(_,null)):Ve&&(Ve.d(1),Ve=null),pe&128&&P!==(P=e[21].PolicyNo+"")&&T(D,P),pe&128&&K!==(K=e[21].CertificateNo+"")&&T(J,K),e[21].InsurerCardNo?Ae?Ae.p(e,pe):(Ae=Un(e),Ae.c(),Ae.m(H,ne)):Ae&&(Ae.d(1),Ae=null),e[21].StaffNo?Re?Re.p(e,pe):(Re=$n(e),Re.c(),Re.m(H,null)):Re&&(Re.d(1),Re=null),pe&128&&re!==(re=e[21].MemberType+"")&&T(se,re),pe&128&&ee!==(ee=e[21].CardType+"")&&T(qe,ee),e[21].CoverageAmount||e[21].Premium?Oe?Oe.p(e,pe):(Oe=qn(e),Oe.c(),Oe.m(Q,gt)):Oe&&(Oe.d(1),Oe=null),pe&128&&ye!==(ye=qt(e[21].PlanEffFrom)+"")&&T(ht,ye),pe&128&&We!==(We=qt(e[21].PlanEffTo)+"")&&T(yt,We),e[21].PlanCode?ze?ze.p(e,pe):(ze=Zn(e),ze.c(),ze.m(we,null)):ze&&(ze.d(1),ze=null),pe&128&&Z!==(Z=(e[21].InsurerName||e[21].InsurerNameEN)+"")&&T(be,Z),pe&128&&st!==(st=e[21].PlanCode+"")&&T(vt,st),pe&128&&Ee!==(Ee=e[21].PlanName+"")&&T(Ne,Ee),pe&128&&Rt!==(Rt="policy-"+e[21].MemberCode+"-title")&&a(l,"aria-labelledby",Rt)},d(Dt){Dt&&E(l),Ve&&Ve.d(),Ae&&Ae.d(),Re&&Re.d(),Oe&&Oe.d(),ze&&ze.d(),Mt=!1,Je()}}}function Us(t){let e,l,n,i,o,s,c,u,f,m,p,b;c=new Os({props:{compact:!1,showLabels:!0,horizontal:!0}});function v(x,V){if(x[0])return Hs;if(x[2]&&x[1])return zs}let _=v(t),C=_&&_(t);const h=[js,Fs,Bs,Vs,Ls],w=[];function S(x,V){return x[3]?x[6]?1:x[5]?2:x[7].length===0?3:4:0}return m=S(t),p=w[m]=h[m](t),{c(){e=d("main"),l=d("div"),n=d("header"),i=d("h1"),i.textContent="กรมธรรม์ประกันภัย",o=g(),s=d("div"),kt(c.$$.fragment),u=g(),C&&C.c(),f=g(),p.c(),a(i,"class","text-3xl font-bold text-gray-900 mb-6"),a(s,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"),a(n,"class","mb-8"),a(l,"class","max-w-7xl mx-auto"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8"),a(e,"aria-label","Insurance Policy List")},m(x,V){k(x,e,V),r(e,l),r(l,n),r(n,i),r(n,o),r(n,s),mt(c,s,null),r(n,u),C&&C.m(n,null),r(l,f),w[m].m(l,null),b=!0},p(x,[V]){_!==(_=v(x))&&(C&&C.d(1),C=_&&_(x),C&&(C.c(),C.m(n,null)));let R=m;m=S(x),m===R?w[m].p(x,V):(Rl(),Ue(w[R],1,1,()=>{w[R]=null}),Ol(),p=w[m],p?p.p(x,V):(p=w[m]=h[m](x),p.c()),He(p,1),p.m(l,null))},i(x){b||(He(c.$$.fragment,x),He(p),b=!0)},o(x){Ue(c.$$.fragment,x),Ue(p),b=!1},d(x){x&&E(e),pt(c),C&&C.d(),w[m].d()}}}function qt(t){if(!t)return"ไม่ระบุ";const e=new Date(t),l=e.getDate().toString().padStart(2,"0"),n=e.toLocaleDateString("th-TH",{month:"long"}),i=e.getFullYear()+543;return`${l} ${n} ${i}`}function ml(t){return!t||t===0?"ไม่ระบุ":new Intl.NumberFormat("th-TH",{style:"currency",currency:"THB",minimumFractionDigits:0,maximumFractionDigits:2}).format(t)}function Ml(t){return t?t.includes("สุขภาพ")?"สุขภาพ":t.includes("ชีวิต")?"ชีวิต":t.includes("Executive")?"เอ็กเซ็กคูทีฟ":t.includes("Premium")?"พรีเมี่ยม":(t.includes("Basic")||t.includes("พื้นฐาน"),"พื้นฐาน"):"ไม่ระบุ"}function $s(t,e,l){let n,i,o,s,c,u,f,m,p,b,v,_,C;ot(t,Pt,N=>l(12,p=N)),ot(t,Wt,N=>l(13,b=N)),ot(t,Kt,N=>l(14,v=N)),ot(t,$i,N=>l(15,_=N)),ot(t,xe,N=>l(16,C=N));const h=Al(),w={Auto:"🚗",Home:"🏠",Life:"❤️",Health:"🏥",Medical:"🏥",Business:"🏢",General:"📄",Basic:"📋",Premium:"⭐",Executive:"💼"},S={Active:"bg-green-100 text-green-800 border-green-200",Inactive:"bg-gray-100 text-gray-800 border-gray-200",Expired:"bg-red-100 text-red-800 border-red-200",Pending:"bg-yellow-100 text-yellow-800 border-yellow-200",Cancelled:"bg-gray-100 text-gray-800 border-gray-200"};function x(N){const A=Ml(N);return w[A]||w.General}function V(N){h("navigate",{page:"policy-detail",memberCode:N})}async function R(){var N;if(!s){console.warn("No member selected, cannot load policies"),Pt.setError(new Error("กรุณาเลือกสมาชิกก่อนดูข้อมูลกรมธรรม์"));return}if(!(s!=null&&s.insurerCode)||!(s!=null&&s.citizenID)){console.warn("Selected member missing required data for API call"),Pt.setError(new Error("ข้อมูลสมาชิกไม่ครบถ้วน"));return}try{const A=s.citizenID,O={INSURER_CODE:s.insurerCode,CITIZEN_ID:A};console.log("Making API call with params:",{insurerCode:s.insurerCode,citizenId:A,originalCitizenCode:s.citizenID}),await as(O),console.log(`Policies loaded successfully for member ${s==null?void 0:s.memberCode}:`,((N=p.data)==null?void 0:N.length)||0)}catch(A){console.error("Failed to load policies:",A)}}async function H(){await R()}ul(()=>{console.log("PolicyList component mounted")});const z=N=>V(N.MemberCode);return t.$$.update=()=>{t.$$.dirty&4096&&l(7,n=p.data||[]),t.$$.dirty&4096&&l(6,i=p.loading),t.$$.dirty&4096&&l(5,o=p.error),t.$$.dirty&65536&&l(0,s=C),t.$$.dirty&32768&&l(4,c=_),t.$$.dirty&16384&&l(2,u=v),t.$$.dirty&8192&&l(1,f=b),t.$$.dirty&6&&l(3,m=u&&f),t.$$.dirty&1&&s&&R()},[s,f,u,m,c,o,i,n,S,x,V,H,p,b,v,_,C,z]}class Ki extends Jt{constructor(e){super(),Zt(this,e,$s,Us,Ht,{})}}function Kn(t,e,l){const n=t.slice();return n[25]=e[l],n[27]=l,n}function Wn(t,e,l){const n=t.slice();return n[28]=e[l],n}function Qn(t,e,l){const n=t.slice();return n[28]=e[l],n}function Xn(t,e,l){const n=t.slice();return n[33]=e[l],n}function ei(t,e,l){const n=t.slice();return n[33]=e[l],n}function qs(t){var Ve,Ae,Re,Oe,ze,ll,Dt,pe,Fl,jl;let e,l,n,i,o,s,c,u,f,m,p,b,v,_,C,h,w,S,x,V,R,H,z,N,A,O,P,D,I,M,F,Y,X,K,J=(((Ve=t[1])==null?void 0:Ve.PolicyNo)||"ไม่ระบุ")+"",W,ne,de,Q,ie,$,L=(((Ae=t[1])==null?void 0:Ae.CertificateNo)||"ไม่ระบุ")+"",U,Te,ge,Fe,fe,re,se=(((Re=t[1])==null?void 0:Re.PlanName)||"ไม่ระบุ")+"",me,oe,le,ae,B,ee,qe=tt((Oe=t[1])==null?void 0:Oe.PlanEffFrom)+"",ut,gt,Le,ve,Nt,we,je=(((ze=t[1])==null?void 0:ze.InsurerName)||((ll=t[1])==null?void 0:ll.InsurerNameEN)||"ไม่ระบุ")+"",lt,bt,Ie,ye,ht,he,ce=tt((Dt=t[1])==null?void 0:Dt.PlanEffTo)+"",Ye,xt,$e,We,yt,dt,ft=(((pe=t[1])==null?void 0:pe.CompanyName)||((Fl=t[1])==null?void 0:Fl.CompanyNameEN)||"ไม่ระบุ")+"",nt,Qe,it,_t,j,Z,be,Ge,Ze,Xe,rt,St,et,st,vt,Me=((jl=t[0])==null?void 0:jl.memberCode)&&ti(t),_e=t[0]&&li(t),De=t[6].length>0&&ri(t),Ce=t[5].length>0&&ai(t),Ee=t[4].length>0&&ci(t),Ne=t[3].length>0&&fi(t),Pe=t[2].length>0&&gi(t);function Rt(G,ke){return G[2].length>0?Ws:Ks}let Mt=Rt(t),Je=Mt(t);return{c(){e=d("main"),l=d("div"),n=d("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,i=g(),o=d("div"),s=d("div"),c=d("div"),u=d("div"),f=d("span"),f.textContent="📄",m=g(),p=d("div"),b=d("h1"),b.textContent="รายละเอียดกรมธรรม์ประกันภัย",v=g(),_=d("div"),C=te("svg"),h=te("path"),w=g(),S=d("div"),x=y("สมาชิก: "),V=d("span"),R=y(t[8]),H=g(),Me&&Me.c(),z=g(),_e&&_e.c(),N=g(),A=d("div"),O=d("div"),P=d("section"),D=d("h2"),D.textContent="ข้อมูลกรมธรรม์",I=g(),M=d("div"),F=d("div"),Y=d("div"),Y.textContent="หมายเลขกรมธรรม์",X=g(),K=d("p"),W=y(J),ne=g(),de=d("div"),Q=d("div"),Q.textContent="หมายเลขใบรับรอง",ie=g(),$=d("p"),U=y(L),Te=g(),ge=d("div"),Fe=d("div"),Fe.textContent="แผนประกันภัย",fe=g(),re=d("p"),me=y(se),oe=g(),le=d("div"),ae=d("div"),ae.textContent="วันที่มีผลบังคับใช้",B=g(),ee=d("p"),ut=y(qe),gt=g(),Le=d("div"),ve=d("div"),ve.textContent="บริษัทประกันภัย",Nt=g(),we=d("p"),lt=y(je),bt=g(),Ie=d("div"),ye=d("div"),ye.textContent="วันที่สิ้นสุด",ht=g(),he=d("p"),Ye=y(ce),xt=g(),$e=d("div"),We=d("div"),We.textContent="บริษัทคู่สัญญา",yt=g(),dt=d("p"),nt=y(ft),Qe=g(),De&&De.c(),it=g(),Ce&&Ce.c(),_t=g(),Ee&&Ee.c(),j=g(),Ne&&Ne.c(),Z=g(),be=d("section"),Ge=d("div"),Ze=d("h2"),Ze.textContent="ประวัติการเคลม",Xe=g(),Pe&&Pe.c(),rt=g(),Je.c(),St=g(),et=d("div"),et.innerHTML="",a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-6 sm:mb-8"),a(f,"class","text-4xl mr-4"),a(f,"aria-hidden","true"),a(b,"class","text-2xl sm:text-3xl font-bold text-gray-900 mb-1"),a(h,"stroke-linecap","round"),a(h,"stroke-linejoin","round"),a(h,"stroke-width","2"),a(h,"d","M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"),a(C,"class","w-4 h-4 text-gray-400 mt-1 mr-2 flex-shrink-0"),a(C,"fill","none"),a(C,"stroke","currentColor"),a(C,"viewBox","0 0 24 24"),a(C,"aria-hidden","true"),a(V,"class","font-medium"),a(S,"class","text-gray-600"),a(_,"class","flex items-start"),a(u,"class","flex items-center mb-4 lg:mb-0"),a(c,"class","flex flex-col lg:flex-row lg:items-center lg:justify-between"),a(s,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"),a(D,"class","text-xl font-semibold text-gray-900 mb-4"),a(Y,"class","text-sm font-medium text-gray-500 mb-1"),a(K,"class","text-lg font-semibold text-gray-900"),a(Q,"class","text-sm font-medium text-gray-500 mb-1"),a($,"class","text-lg font-semibold text-gray-900"),a(Fe,"class","text-sm font-medium text-gray-500 mb-1"),a(re,"class","text-gray-900"),a(ae,"class","text-sm font-medium text-gray-500 mb-1"),a(ee,"class","text-gray-900"),a(ve,"class","text-sm font-medium text-gray-500 mb-1"),a(we,"class","text-gray-900"),a(ye,"class","text-sm font-medium text-gray-500 mb-1"),a(he,"class","text-gray-900"),a(M,"class","grid gap-4 sm:grid-cols-2"),a(We,"class","text-sm font-medium text-gray-500 mb-2"),a(dt,"class","text-gray-700 leading-relaxed"),a($e,"class","mt-4 pt-4 border-t border-gray-200"),a(P,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(Ze,"id","claims-history-heading"),a(Ze,"class","text-xl font-semibold text-gray-900 mb-2 sm:mb-0"),a(Ge,"class","flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6"),a(be,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(be,"aria-labelledby","claims-history-heading"),a(O,"class","lg:col-span-3 xl:col-span-4 space-y-6"),a(et,"class","lg:col-span-1 xl:col-span-1 space-y-6"),a(A,"class","grid gap-6 grid-cols-1 lg:grid-cols-3 xl:grid-cols-4"),a(o,"class","max-w-7xl mx-auto"),a(e,"class","min-h-screen bg-gray-50 py-4 px-4 sm:py-8 sm:px-6 lg:px-8")},m(G,ke){k(G,e,ke),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(s,c),r(c,u),r(u,f),r(u,m),r(u,p),r(p,b),r(p,v),r(p,_),r(_,C),r(C,h),r(_,w),r(_,S),r(S,x),r(S,V),r(V,R),r(S,H),Me&&Me.m(S,null),r(c,z),_e&&_e.m(c,null),r(o,N),r(o,A),r(A,O),r(O,P),r(P,D),r(P,I),r(P,M),r(M,F),r(F,Y),r(F,X),r(F,K),r(K,W),r(M,ne),r(M,de),r(de,Q),r(de,ie),r(de,$),r($,U),r(M,Te),r(M,ge),r(ge,Fe),r(ge,fe),r(ge,re),r(re,me),r(M,oe),r(M,le),r(le,ae),r(le,B),r(le,ee),r(ee,ut),r(M,gt),r(M,Le),r(Le,ve),r(Le,Nt),r(Le,we),r(we,lt),r(M,bt),r(M,Ie),r(Ie,ye),r(Ie,ht),r(Ie,he),r(he,Ye),r(P,xt),r(P,$e),r($e,We),r($e,yt),r($e,dt),r(dt,nt),r(O,Qe),De&&De.m(O,null),r(O,it),Ce&&Ce.m(O,null),r(O,_t),Ee&&Ee.m(O,null),r(O,j),Ne&&Ne.m(O,null),r(O,Z),r(O,be),r(be,Ge),r(Ge,Ze),r(Ge,Xe),Pe&&Pe.m(Ge,null),r(be,rt),Je.m(be,null),r(A,St),r(A,et),st||(vt=Se(n,"click",t[11]),st=!0)},p(G,ke){var Ul,$l,ql,Yl,Gl,Zl,Jl,Kl,Wl,Ql;ke[0]&256&&T(R,G[8]),(Ul=G[0])!=null&&Ul.memberCode?Me?Me.p(G,ke):(Me=ti(G),Me.c(),Me.m(S,null)):Me&&(Me.d(1),Me=null),G[0]?_e?_e.p(G,ke):(_e=li(G),_e.c(),_e.m(c,null)):_e&&(_e.d(1),_e=null),ke[0]&2&&J!==(J=((($l=G[1])==null?void 0:$l.PolicyNo)||"ไม่ระบุ")+"")&&T(W,J),ke[0]&2&&L!==(L=(((ql=G[1])==null?void 0:ql.CertificateNo)||"ไม่ระบุ")+"")&&T(U,L),ke[0]&2&&se!==(se=(((Yl=G[1])==null?void 0:Yl.PlanName)||"ไม่ระบุ")+"")&&T(me,se),ke[0]&2&&qe!==(qe=tt((Gl=G[1])==null?void 0:Gl.PlanEffFrom)+"")&&T(ut,qe),ke[0]&2&&je!==(je=(((Zl=G[1])==null?void 0:Zl.InsurerName)||((Jl=G[1])==null?void 0:Jl.InsurerNameEN)||"ไม่ระบุ")+"")&&T(lt,je),ke[0]&2&&ce!==(ce=tt((Kl=G[1])==null?void 0:Kl.PlanEffTo)+"")&&T(Ye,ce),ke[0]&2&&ft!==(ft=(((Wl=G[1])==null?void 0:Wl.CompanyName)||((Ql=G[1])==null?void 0:Ql.CompanyNameEN)||"ไม่ระบุ")+"")&&T(nt,ft),G[6].length>0?De?De.p(G,ke):(De=ri(G),De.c(),De.m(O,it)):De&&(De.d(1),De=null),G[5].length>0?Ce?Ce.p(G,ke):(Ce=ai(G),Ce.c(),Ce.m(O,_t)):Ce&&(Ce.d(1),Ce=null),G[4].length>0?Ee?Ee.p(G,ke):(Ee=ci(G),Ee.c(),Ee.m(O,j)):Ee&&(Ee.d(1),Ee=null),G[3].length>0?Ne?Ne.p(G,ke):(Ne=fi(G),Ne.c(),Ne.m(O,Z)):Ne&&(Ne.d(1),Ne=null),G[2].length>0?Pe?Pe.p(G,ke):(Pe=gi(G),Pe.c(),Pe.m(Ge,null)):Pe&&(Pe.d(1),Pe=null),Mt===(Mt=Rt(G))&&Je?Je.p(G,ke):(Je.d(1),Je=Mt(G),Je&&(Je.c(),Je.m(be,null)))},i:q,o:q,d(G){G&&E(e),Me&&Me.d(),_e&&_e.d(),De&&De.d(),Ce&&Ce.d(),Ee&&Ee.d(),Ne&&Ne.d(),Pe&&Pe.d(),Je.d(),st=!1,vt()}}}function Ys(t){let e,l,n,i,o,s,c,u;return o=new Bl({props:{variant:"not-found",message:"ไม่พบข้อมูลกรมธรรม์ที่ร้องขอ"}}),o.$on("goBack",t[11]),{c(){e=d("main"),l=d("div"),n=d("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,i=g(),kt(o.$$.fragment),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(e,i),mt(o,e,null),s=!0,c||(u=Se(n,"click",t[11]),c=!0)},p:q,i(f){s||(He(o.$$.fragment,f),s=!0)},o(f){Ue(o.$$.fragment,f),s=!1},d(f){f&&E(e),pt(o),c=!1,u()}}}function Gs(t){let e,l,n,i,o,s,c,u;return o=new Bl({props:{variant:"api",error:t[9],message:"ไม่สามารถโหลดรายละเอียดกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"}}),o.$on("retry",t[15]),o.$on("goBack",t[11]),{c(){e=d("main"),l=d("div"),n=d("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,i=g(),kt(o.$$.fragment),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(e,i),mt(o,e,null),s=!0,c||(u=Se(n,"click",t[11]),c=!0)},p(f,m){const p={};m[0]&512&&(p.error=f[9]),o.$set(p)},i(f){s||(He(o.$$.fragment,f),s=!0)},o(f){Ue(o.$$.fragment,f),s=!1},d(f){f&&E(e),pt(o),c=!1,u()}}}function Zs(t){let e,l,n,i,o,s,c,u;return o=new Ji({props:{variant:"detail",message:"กำลังโหลดรายละเอียดกรมธรรม์..."}}),{c(){e=d("main"),l=d("div"),n=d("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,i=g(),kt(o.$$.fragment),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(f,m){k(f,e,m),r(e,l),r(l,n),r(e,i),mt(o,e,null),s=!0,c||(u=Se(n,"click",t[11]),c=!0)},p:q,i(f){s||(He(o.$$.fragment,f),s=!0)},o(f){Ue(o.$$.fragment,f),s=!1},d(f){f&&E(e),pt(o),c=!1,u()}}}function Js(t){let e,l,n,i,o,s,c,u,f,m,p,b,v,_;return{c(){e=d("main"),l=d("div"),n=d("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,i=g(),o=d("div"),s=d("div"),s.textContent="�",c=g(),u=d("h1"),u.textContent="ไม่ได้เลือกสมาชิก",f=g(),m=d("p"),m.textContent="กรุณาเลือกสมาชิกจากเมนูด้านบนเพื่อดูรายละเอียดกรมธรรม์",p=g(),b=d("button"),b.textContent="ดูรายการกรมธรรม์",a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(s,"class","text-8xl mb-6"),a(s,"aria-hidden","true"),a(u,"class","text-4xl font-bold text-gray-900 mb-4"),a(m,"class","text-xl text-gray-600 max-w-2xl mx-auto mb-8"),a(b,"class","inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(b,"aria-label","ไปยังรายการกรมธรรม์"),a(o,"class","max-w-4xl mx-auto text-center"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(C,h){k(C,e,h),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(o,c),r(o,u),r(o,f),r(o,m),r(o,p),r(o,b),v||(_=[Se(n,"click",t[11]),Se(b,"click",t[11])],v=!0)},p:q,i:q,o:q,d(C){C&&E(e),v=!1,Et(_)}}}function ti(t){let e,l,n=t[0].memberCode+"",i,o;return{c(){e=d("span"),l=y("(รหัสสมาชิก: "),i=y(n),o=y(")"),a(e,"class","text-sm text-gray-500 ml-2")},m(s,c){k(s,e,c),r(e,l),r(e,i),r(e,o)},p(s,c){c[0]&1&&n!==(n=s[0].memberCode+"")&&T(i,n)},d(s){s&&E(e)}}}function li(t){let e,l,n=t[0].memberStatus+"",i,o,s,c,u,f=t[0].vip==="Y"&&ni(),m=t[0].cardType&&t[0].cardType!=="Standard"&&ii(t);return{c(){e=d("div"),l=d("span"),i=y(n),c=g(),f&&f.c(),u=g(),m&&m.c(),a(l,"class",o="px-4 py-2 rounded-full text-sm font-medium border text-center "+(t[12][t[0].memberStatus]||t[12].Active)),a(l,"aria-label",s="สถานะสมาชิก: "+t[0].memberStatus),a(e,"class","flex flex-col sm:flex-row sm:items-center gap-3")},m(p,b){k(p,e,b),r(e,l),r(l,i),r(e,c),f&&f.m(e,null),r(e,u),m&&m.m(e,null)},p(p,b){b[0]&1&&n!==(n=p[0].memberStatus+"")&&T(i,n),b[0]&1&&o!==(o="px-4 py-2 rounded-full text-sm font-medium border text-center "+(p[12][p[0].memberStatus]||p[12].Active))&&a(l,"class",o),b[0]&1&&s!==(s="สถานะสมาชิก: "+p[0].memberStatus)&&a(l,"aria-label",s),p[0].vip==="Y"?f||(f=ni(),f.c(),f.m(e,u)):f&&(f.d(1),f=null),p[0].cardType&&p[0].cardType!=="Standard"?m?m.p(p,b):(m=ii(p),m.c(),m.m(e,null)):m&&(m.d(1),m=null)},d(p){p&&E(e),f&&f.d(),m&&m.d()}}}function ni(t){let e;return{c(){e=d("span"),e.textContent="VIP",a(e,"class","px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200")},m(l,n){k(l,e,n)},d(l){l&&E(e)}}}function ii(t){let e,l=t[0].cardType+"",n,i;return{c(){e=d("span"),n=y(l),a(e,"class",i="px-3 py-1 rounded-full text-xs font-medium "+(t[0].cardType==="Gold"?"bg-yellow-100 text-yellow-800 border border-yellow-200":t[0].cardType==="Platinum"?"bg-gray-100 text-gray-800 border border-gray-200":t[0].cardType==="Diamond"?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-800 border border-gray-200"))},m(o,s){k(o,e,s),r(e,n)},p(o,s){s[0]&1&&l!==(l=o[0].cardType+"")&&T(n,l),s[0]&1&&i!==(i="px-3 py-1 rounded-full text-xs font-medium "+(o[0].cardType==="Gold"?"bg-yellow-100 text-yellow-800 border border-yellow-200":o[0].cardType==="Platinum"?"bg-gray-100 text-gray-800 border border-gray-200":o[0].cardType==="Diamond"?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-800 border border-gray-200"))&&a(e,"class",i)},d(o){o&&E(e)}}}function ri(t){let e,l,n,i,o=ue(t[6]),s=[];for(let c=0;c<o.length;c+=1)s[c]=si(ei(t,o,c));return{c(){e=d("section"),l=d("h2"),l.textContent="ความคุ้มครองหลัก",n=g(),i=d("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(i,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(i,null)},p(c,u){if(u[0]&64){o=ue(c[6]);let f;for(f=0;f<o.length;f+=1){const m=ei(c,o,f);s[f]?s[f].p(m,u):(s[f]=si(m),s[f].c(),s[f].m(i,null))}for(;f<s.length;f+=1)s[f].d(1);s.length=o.length}},d(c){c&&E(e),Ke(s,c)}}}function si(t){let e,l,n,i,o=t[33].MainBenefit+"",s,c,u,f=t[33].MainBenefitEN+"",m,p,b,v=t[33].MainPlanLimitDesc+"",_,C,h,w,S=Be(parseInt(t[33].MainPlanAmount)||0)+"",x,V,R,H=t[33].MainPlanUnit1+"",z,N,A=t[33].MainPlanUnit2+"",O,P,D,I,M=Be(parseInt(t[33].MainPlanBalance)||0)+"",F,Y;return{c(){e=d("div"),l=d("div"),n=d("div"),i=d("h3"),s=y(o),c=g(),u=d("p"),m=y(f),p=g(),b=d("p"),_=y(v),C=g(),h=d("div"),w=d("div"),x=y(S),V=g(),R=d("div"),z=y(H),N=y("/"),O=y(A),P=g(),D=d("div"),I=y("คงเหลือ: "),F=y(M),Y=g(),a(i,"class","font-medium text-gray-900"),a(u,"class","text-sm text-gray-600 mt-1"),a(b,"class","text-sm text-gray-500 mt-1"),a(w,"class","text-lg font-semibold text-blue-600"),a(R,"class","text-sm text-gray-500"),a(D,"class","text-sm text-green-600 mt-1"),a(h,"class","text-right"),a(l,"class","flex justify-between items-start"),a(e,"class","p-4 bg-blue-50 rounded-lg border border-blue-200")},m(X,K){k(X,e,K),r(e,l),r(l,n),r(n,i),r(i,s),r(n,c),r(n,u),r(u,m),r(n,p),r(n,b),r(b,_),r(l,C),r(l,h),r(h,w),r(w,x),r(h,V),r(h,R),r(R,z),r(R,N),r(R,O),r(h,P),r(h,D),r(D,I),r(D,F),r(e,Y)},p(X,K){K[0]&64&&o!==(o=X[33].MainBenefit+"")&&T(s,o),K[0]&64&&f!==(f=X[33].MainBenefitEN+"")&&T(m,f),K[0]&64&&v!==(v=X[33].MainPlanLimitDesc+"")&&T(_,v),K[0]&64&&S!==(S=Be(parseInt(X[33].MainPlanAmount)||0)+"")&&T(x,S),K[0]&64&&H!==(H=X[33].MainPlanUnit1+"")&&T(z,H),K[0]&64&&A!==(A=X[33].MainPlanUnit2+"")&&T(O,A),K[0]&64&&M!==(M=Be(parseInt(X[33].MainPlanBalance)||0)+"")&&T(F,M)},d(X){X&&E(e)}}}function ai(t){let e,l,n,i,o=ue(t[5]),s=[];for(let c=0;c<o.length;c+=1)s[c]=oi(Xn(t,o,c));return{c(){e=d("section"),l=d("h2"),l.textContent="รายละเอียดความคุ้มครอง",n=g(),i=d("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(i,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(i,null)},p(c,u){if(u[0]&32){o=ue(c[5]);let f;for(f=0;f<o.length;f+=1){const m=Xn(c,o,f);s[f]?s[f].p(m,u):(s[f]=oi(m),s[f].c(),s[f].m(i,null))}for(;f<s.length;f+=1)s[f].d(1);s.length=o.length}},d(c){c&&E(e),Ke(s,c)}}}function oi(t){let e,l,n,i,o=t[33].BenefitTH+"",s,c,u,f=t[33].BenefitEN+"",m,p,b,v=t[33].SubBenefitTH+"",_,C,h=t[33].SubBenefitEN+"",w,S,x,V,R,H,z,N,A=Be(parseInt(t[33].LimitAmt)||0)+"",O,P,D,I,M,F,Y=Be(parseInt(t[33].ComLimitAmt)||0)+"",X,K,J,W,ne,de,Q=Be(parseInt(t[33].BalComLimitAmt)||0)+"",ie,$;return{c(){e=d("div"),l=d("div"),n=d("div"),i=d("h3"),s=y(o),c=g(),u=d("p"),m=y(f),p=g(),b=d("p"),_=y(v),C=y(" ("),w=y(h),S=y(")"),x=g(),V=d("div"),R=d("div"),H=d("span"),H.textContent="วงเงินต่อครั้ง:",z=g(),N=d("span"),O=y(A),P=g(),D=d("div"),I=d("span"),I.textContent="วงเงินต่อปี:",M=g(),F=d("span"),X=y(Y),K=g(),J=d("div"),W=d("span"),W.textContent="คงเหลือต่อปี:",ne=g(),de=d("span"),ie=y(Q),$=g(),a(i,"class","font-medium text-gray-900"),a(u,"class","text-sm text-gray-600"),a(b,"class","text-sm text-gray-500 mt-1"),a(H,"class","text-sm text-gray-500"),a(N,"class","text-sm font-medium"),a(R,"class","flex justify-between"),a(I,"class","text-sm text-gray-500"),a(F,"class","text-sm font-medium"),a(D,"class","flex justify-between"),a(W,"class","text-sm text-gray-500"),a(de,"class","text-sm font-medium text-green-600"),a(J,"class","flex justify-between"),a(V,"class","space-y-2"),a(l,"class","grid gap-4 md:grid-cols-2"),a(e,"class","p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors")},m(L,U){k(L,e,U),r(e,l),r(l,n),r(n,i),r(i,s),r(n,c),r(n,u),r(u,m),r(n,p),r(n,b),r(b,_),r(b,C),r(b,w),r(b,S),r(l,x),r(l,V),r(V,R),r(R,H),r(R,z),r(R,N),r(N,O),r(V,P),r(V,D),r(D,I),r(D,M),r(D,F),r(F,X),r(V,K),r(V,J),r(J,W),r(J,ne),r(J,de),r(de,ie),r(e,$)},p(L,U){U[0]&32&&o!==(o=L[33].BenefitTH+"")&&T(s,o),U[0]&32&&f!==(f=L[33].BenefitEN+"")&&T(m,f),U[0]&32&&v!==(v=L[33].SubBenefitTH+"")&&T(_,v),U[0]&32&&h!==(h=L[33].SubBenefitEN+"")&&T(w,h),U[0]&32&&A!==(A=Be(parseInt(L[33].LimitAmt)||0)+"")&&T(O,A),U[0]&32&&Y!==(Y=Be(parseInt(L[33].ComLimitAmt)||0)+"")&&T(X,Y),U[0]&32&&Q!==(Q=Be(parseInt(L[33].BalComLimitAmt)||0)+"")&&T(ie,Q)},d(L){L&&E(e)}}}function ci(t){let e,l,n,i,o=ue(t[4]),s=[];for(let c=0;c<o.length;c+=1)s[c]=di(Qn(t,o,c));return{c(){e=d("section"),l=d("h2"),l.textContent="เงื่อนไขสัญญา",n=g(),i=d("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(i,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(i,null)},p(c,u){if(u[0]&16){o=ue(c[4]);let f;for(f=0;f<o.length;f+=1){const m=Qn(c,o,f);s[f]?s[f].p(m,u):(s[f]=di(m),s[f].c(),s[f].m(i,null))}for(;f<s.length;f+=1)s[f].d(1);s.length=o.length}},d(c){c&&E(e),Ke(s,c)}}}function ui(t){let e,l,n=t[28].Remarks+"",i;return{c(){e=d("p"),l=y("หมายเหตุ: "),i=y(n),a(e,"class","text-xs text-gray-500 mt-2 italic")},m(o,s){k(o,e,s),r(e,l),r(e,i)},p(o,s){s[0]&16&&n!==(n=o[28].Remarks+"")&&T(i,n)},d(o){o&&E(e)}}}function di(t){let e,l,n,i=(t[28].ConditionType==="Exclusion"?"ข้อยกเว้น":t[28].ConditionType==="Waiting Period"?"ระยะเวลารอคุ้มครอง":t[28].ConditionType)+"",o,s,c,u=t[28].ConditionApply==="Y"?"มีผล":"ไม่มีผล",f,m,p,b,v=t[28].ConditionDetail+"",_,C,h,w,S,x=tt(t[28].EffFromDate)+"",V,R,H,z,N=tt(t[28].EffToDate)+"",A,O,P,D,I=t[28].Remarks&&ui(t);return{c(){e=d("div"),l=d("div"),n=d("h3"),o=y(i),s=g(),c=d("span"),f=y(u),p=g(),b=d("p"),_=y(v),C=g(),h=d("div"),w=d("div"),S=y("วันที่เริ่มต้น: "),V=y(x),R=g(),H=d("div"),z=y("วันที่สิ้นสุด: "),A=y(N),O=g(),I&&I.c(),P=g(),a(n,"class","font-medium text-gray-900"),a(c,"class",m="px-2 py-1 text-xs rounded-full "+(t[28].ConditionApply==="Y"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800")),a(l,"class","flex justify-between items-start mb-2"),a(b,"class","text-sm text-gray-700 mb-2"),a(h,"class","grid gap-2 sm:grid-cols-2 text-xs text-gray-500"),a(e,"class",D="p-4 border border-gray-200 rounded-lg "+(t[28].ConditionType==="Exclusion"?"bg-red-50 border-red-200":t[28].ConditionType==="Waiting Period"?"bg-yellow-50 border-yellow-200":"bg-gray-50"))},m(M,F){k(M,e,F),r(e,l),r(l,n),r(n,o),r(l,s),r(l,c),r(c,f),r(e,p),r(e,b),r(b,_),r(e,C),r(e,h),r(h,w),r(w,S),r(w,V),r(h,R),r(h,H),r(H,z),r(H,A),r(e,O),I&&I.m(e,null),r(e,P)},p(M,F){F[0]&16&&i!==(i=(M[28].ConditionType==="Exclusion"?"ข้อยกเว้น":M[28].ConditionType==="Waiting Period"?"ระยะเวลารอคุ้มครอง":M[28].ConditionType)+"")&&T(o,i),F[0]&16&&u!==(u=M[28].ConditionApply==="Y"?"มีผล":"ไม่มีผล")&&T(f,u),F[0]&16&&m!==(m="px-2 py-1 text-xs rounded-full "+(M[28].ConditionApply==="Y"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"))&&a(c,"class",m),F[0]&16&&v!==(v=M[28].ConditionDetail+"")&&T(_,v),F[0]&16&&x!==(x=tt(M[28].EffFromDate)+"")&&T(V,x),F[0]&16&&N!==(N=tt(M[28].EffToDate)+"")&&T(A,N),M[28].Remarks?I?I.p(M,F):(I=ui(M),I.c(),I.m(e,P)):I&&(I.d(1),I=null),F[0]&16&&D!==(D="p-4 border border-gray-200 rounded-lg "+(M[28].ConditionType==="Exclusion"?"bg-red-50 border-red-200":M[28].ConditionType==="Waiting Period"?"bg-yellow-50 border-yellow-200":"bg-gray-50"))&&a(e,"class",D)},d(M){M&&E(e),I&&I.d()}}}function fi(t){let e,l,n,i,o=ue(t[3]),s=[];for(let c=0;c<o.length;c+=1)s[c]=pi(Wn(t,o,c));return{c(){e=d("section"),l=d("h2"),l.textContent="เงื่อนไขเฉพาะสมาชิก",n=g(),i=d("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(i,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){k(c,e,u),r(e,l),r(e,n),r(e,i);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(i,null)},p(c,u){if(u[0]&8){o=ue(c[3]);let f;for(f=0;f<o.length;f+=1){const m=Wn(c,o,f);s[f]?s[f].p(m,u):(s[f]=pi(m),s[f].c(),s[f].m(i,null))}for(;f<s.length;f+=1)s[f].d(1);s.length=o.length}},d(c){c&&E(e),Ke(s,c)}}}function mi(t){let e,l,n=t[28].Remarks+"",i;return{c(){e=d("p"),l=y("หมายเหตุ: "),i=y(n),a(e,"class","text-xs text-gray-500 mt-2 italic")},m(o,s){k(o,e,s),r(e,l),r(e,i)},p(o,s){s[0]&8&&n!==(n=o[28].Remarks+"")&&T(i,n)},d(o){o&&E(e)}}}function pi(t){let e,l,n,i=(t[28].ConditionType==="Medical History"?"ประวัติการรักษา":t[28].ConditionType==="Medication"?"การใช้ยา":t[28].ConditionType)+"",o,s,c,u=(t[28].Action==="Monitor"?"ต้องติดตาม":t[28].Action==="Alert"?"เฝ้าระวัง":t[28].Action)+"",f,m,p,b,v=t[28].ConditionDetail+"",_,C,h,w,S,x=tt(t[28].EffFromDate)+"",V,R,H,z,N=tt(t[28].EffToDate)+"",A,O,P,D,I=t[28].Remarks&&mi(t);return{c(){e=d("div"),l=d("div"),n=d("h3"),o=y(i),s=g(),c=d("span"),f=y(u),p=g(),b=d("p"),_=y(v),C=g(),h=d("div"),w=d("div"),S=y("วันที่เริ่มต้น: "),V=y(x),R=g(),H=d("div"),z=y("วันที่สิ้นสุด: "),A=y(N),O=g(),I&&I.c(),P=g(),a(n,"class","font-medium text-gray-900"),a(c,"class",m="px-2 py-1 text-xs rounded-full "+(t[28].Action==="Monitor"?"bg-yellow-100 text-yellow-800":t[28].Action==="Alert"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800")),a(l,"class","flex justify-between items-start mb-2"),a(b,"class","text-sm text-gray-700 mb-2"),a(h,"class","grid gap-2 sm:grid-cols-2 text-xs text-gray-500"),a(e,"class",D="p-4 border border-gray-200 rounded-lg "+(t[28].ConditionType==="Medical History"?"bg-blue-50 border-blue-200":t[28].ConditionType==="Medication"?"bg-purple-50 border-purple-200":"bg-gray-50"))},m(M,F){k(M,e,F),r(e,l),r(l,n),r(n,o),r(l,s),r(l,c),r(c,f),r(e,p),r(e,b),r(b,_),r(e,C),r(e,h),r(h,w),r(w,S),r(w,V),r(h,R),r(h,H),r(H,z),r(H,A),r(e,O),I&&I.m(e,null),r(e,P)},p(M,F){F[0]&8&&i!==(i=(M[28].ConditionType==="Medical History"?"ประวัติการรักษา":M[28].ConditionType==="Medication"?"การใช้ยา":M[28].ConditionType)+"")&&T(o,i),F[0]&8&&u!==(u=(M[28].Action==="Monitor"?"ต้องติดตาม":M[28].Action==="Alert"?"เฝ้าระวัง":M[28].Action)+"")&&T(f,u),F[0]&8&&m!==(m="px-2 py-1 text-xs rounded-full "+(M[28].Action==="Monitor"?"bg-yellow-100 text-yellow-800":M[28].Action==="Alert"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"))&&a(c,"class",m),F[0]&8&&v!==(v=M[28].ConditionDetail+"")&&T(_,v),F[0]&8&&x!==(x=tt(M[28].EffFromDate)+"")&&T(V,x),F[0]&8&&N!==(N=tt(M[28].EffToDate)+"")&&T(A,N),M[28].Remarks?I?I.p(M,F):(I=mi(M),I.c(),I.m(e,P)):I&&(I.d(1),I=null),F[0]&8&&D!==(D="p-4 border border-gray-200 rounded-lg "+(M[28].ConditionType==="Medical History"?"bg-blue-50 border-blue-200":M[28].ConditionType==="Medication"?"bg-purple-50 border-purple-200":"bg-gray-50"))&&a(e,"class",D)},d(M){M&&E(e),I&&I.d()}}}function gi(t){let e,l,n=t[2].length+"",i,o;return{c(){e=d("div"),l=y("ทั้งหมด "),i=y(n),o=y(" รายการ"),a(e,"class","text-sm text-gray-500")},m(s,c){k(s,e,c),r(e,l),r(e,i),r(e,o)},p(s,c){c[0]&4&&n!==(n=s[2].length+"")&&T(i,n)},d(s){s&&E(e)}}}function Ks(t){let e;return{c(){e=d("div"),e.innerHTML=`<div class="text-6xl mb-4" aria-hidden="true">📋</div> <h3 class="text-lg font-medium text-gray-900 mb-2">ไม่พบประวัติการเคลม</h3> <p class="text-gray-500 max-w-md mx-auto">ยังไม่มีประวัติการเคลมสำหรับสมาชิกนี้
                  หรือข้อมูลอาจยังไม่ได้รับการอัปเดต</p>`,a(e,"class","text-center py-12")},m(l,n){k(l,e,n)},p:q,d(l){l&&E(e)}}}function Ws(t){let e,l=ue(t[2]),n=[];for(let i=0;i<l.length;i+=1)n[i]=Ei(Kn(t,l,i));return{c(){e=d("div");for(let i=0;i<n.length;i+=1)n[i].c();a(e,"class","space-y-4")},m(i,o){k(i,e,o);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null)},p(i,o){if(o[0]&24580){l=ue(i[2]);let s;for(s=0;s<l.length;s+=1){const c=Kn(i,l,s);n[s]?n[s].p(c,o):(n[s]=Ei(c),n[s].c(),n[s].m(e,null))}for(;s<n.length;s+=1)n[s].d(1);n.length=l.length}},d(i){i&&E(e),Ke(n,i)}}}function bi(t){let e,l=sl(t[25].ClaimType||t[25].ServiceType)+"",n,i,o;return{c(){e=d("span"),n=y(l),a(e,"class",i="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border "+t[14](t[25].ClaimType||t[25].ServiceType)),a(e,"aria-label",o="ประเภทเคลม: "+sl(t[25].ClaimType||t[25].ServiceType))},m(s,c){k(s,e,c),r(e,n)},p(s,c){c[0]&4&&l!==(l=sl(s[25].ClaimType||s[25].ServiceType)+"")&&T(n,l),c[0]&4&&i!==(i="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border "+s[14](s[25].ClaimType||s[25].ServiceType))&&a(e,"class",i),c[0]&4&&o!==(o="ประเภทเคลม: "+sl(s[25].ClaimType||s[25].ServiceType))&&a(e,"aria-label",o)},d(s){s&&E(e)}}}function hi(t){let e,l,n,i,o,s=Be(parseInt(t[25].IncurredAmt)-parseInt(t[25].PayableAmt))+"",c;return{c(){e=d("div"),l=d("div"),l.textContent="ส่วนต่าง",n=g(),i=d("div"),o=y("-"),c=y(s),a(l,"class","text-sm text-gray-500 mb-1"),a(i,"class","text-sm font-medium text-red-600")},m(u,f){k(u,e,f),r(e,l),r(e,n),r(e,i),r(i,o),r(i,c)},p(u,f){f[0]&4&&s!==(s=Be(parseInt(u[25].IncurredAmt)-parseInt(u[25].PayableAmt))+"")&&T(c,s)},d(u){u&&E(e)}}}function yi(t){let e,l,n,i,o,s,c,u,f=Gt(t[25].VisitDate||t[25].ServiceDate)+"",m;return{c(){e=d("div"),l=te("svg"),n=te("path"),i=g(),o=d("div"),s=d("div"),s.textContent="วันที่เข้ารับบริการ",c=g(),u=d("div"),m=y(f),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"),a(l,"class","w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(l,"aria-hidden","true"),a(s,"class","font-medium text-gray-700"),a(u,"class","text-gray-600"),a(e,"class","flex items-start")},m(p,b){k(p,e,b),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(o,c),r(o,u),r(u,m)},p(p,b){b[0]&4&&f!==(f=Gt(p[25].VisitDate||p[25].ServiceDate)+"")&&T(m,f)},d(p){p&&E(e)}}}function _i(t){let e,l,n,i,o,s,c,u,f=Gt(t[25].ClaimDate)+"",m;return{c(){e=d("div"),l=te("svg"),n=te("path"),i=g(),o=d("div"),s=d("div"),s.textContent="วันที่ยื่นเคลม",c=g(),u=d("div"),m=y(f),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"),a(l,"class","w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(l,"aria-hidden","true"),a(s,"class","font-medium text-gray-700"),a(u,"class","text-gray-600"),a(e,"class","flex items-start")},m(p,b){k(p,e,b),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(o,c),r(o,u),r(u,m)},p(p,b){b[0]&4&&f!==(f=Gt(p[25].ClaimDate)+"")&&T(m,f)},d(p){p&&E(e)}}}function vi(t){let e,l,n,i,o,s,c,u,f=Gt(t[25].SettlementDate)+"",m;return{c(){e=d("div"),l=te("svg"),n=te("path"),i=g(),o=d("div"),s=d("div"),s.textContent="วันที่จ่ายเงิน",c=g(),u=d("div"),m=y(f),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"),a(l,"class","w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(l,"aria-hidden","true"),a(s,"class","font-medium text-gray-700"),a(u,"class","text-gray-600"),a(e,"class","flex items-start")},m(p,b){k(p,e,b),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(o,c),r(o,u),r(u,m)},p(p,b){b[0]&4&&f!==(f=Gt(p[25].SettlementDate)+"")&&T(m,f)},d(p){p&&E(e)}}}function wi(t){let e,l,n,i,o,s,c,u,f=(t[25].ProviderTH||t[25].ProviderEN||t[25].ProviderName||"ไม่ระบุ")+"",m;return{c(){e=d("div"),l=te("svg"),n=te("path"),i=g(),o=d("div"),s=d("div"),s.textContent="สถานพยาบาล/ผู้ให้บริการ",c=g(),u=d("div"),m=y(f),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"),a(l,"class","w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(l,"aria-hidden","true"),a(s,"class","font-medium text-gray-700"),a(u,"class","text-gray-600"),a(e,"class","flex items-start")},m(p,b){k(p,e,b),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(o,c),r(o,u),r(u,m)},p(p,b){b[0]&4&&f!==(f=(p[25].ProviderTH||p[25].ProviderEN||p[25].ProviderName||"ไม่ระบุ")+"")&&T(m,f)},d(p){p&&E(e)}}}function Ci(t){let e,l,n,i,o,s,c,u,f=t[25].PolicyNo+"",m;return{c(){e=d("div"),l=te("svg"),n=te("path"),i=g(),o=d("div"),s=d("div"),s.textContent="หมายเลขกรมธรรม์",c=g(),u=d("div"),m=y(f),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"),a(l,"class","w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(l,"aria-hidden","true"),a(s,"class","font-medium text-gray-700"),a(u,"class","text-gray-600 font-mono"),a(e,"class","flex items-start")},m(p,b){k(p,e,b),r(e,l),r(l,n),r(e,i),r(e,o),r(o,s),r(o,c),r(o,u),r(u,m)},p(p,b){b[0]&4&&f!==(f=p[25].PolicyNo+"")&&T(m,f)},d(p){p&&E(e)}}}function Ei(t){let e,l,n,i,o,s,c=(t[25].ClaimNo||t[25].ClaimID||"ไม่ระบุ")+"",u,f,m,p,b=rl(t[25].ClaimStatus)+"",v,_,C,h,w,S,x,V=(t[25].DiagTH||t[25].DiagEN||t[25].Description||"ไม่ระบุรายละเอียด")+"",R,H,z,N,A,O,P,D,I=Be(parseInt(t[25].PayableAmt)||0)+"",M,F,Y,X,K,J,W=Be(parseInt(t[25].IncurredAmt)||0)+"",ne,de,Q=t[25].PayableAmt&&t[25].IncurredAmt&&parseInt(t[25].PayableAmt)!==parseInt(t[25].IncurredAmt),ie,$,L,U,Te,ge,Fe,fe=(t[25].ClaimType||t[25].ServiceType)&&bi(t),re=Q&&hi(t),se=(t[25].VisitDate||t[25].ServiceDate)&&yi(t),me=t[25].ClaimDate&&_i(t),oe=t[25].SettlementDate&&vi(t),le=(t[25].ProviderTH||t[25].ProviderEN||t[25].ProviderName)&&wi(t),ae=t[25].PolicyNo&&Ci(t);return{c(){e=d("article"),l=d("div"),n=d("div"),i=d("div"),o=d("h3"),s=y("เลขที่ "),u=y(c),f=g(),m=d("div"),p=d("span"),v=y(b),h=g(),fe&&fe.c(),w=g(),S=d("div"),x=d("p"),R=y(V),H=g(),z=d("div"),N=d("div"),A=d("div"),O=d("div"),O.textContent="จำนวนที่จ่าย",P=g(),D=d("div"),M=y(I),F=g(),Y=d("div"),X=d("div"),X.textContent="จำนวนที่เรียกร้อง",K=g(),J=d("div"),ne=y(W),de=g(),re&&re.c(),ie=g(),$=d("div"),se&&se.c(),L=g(),me&&me.c(),U=g(),oe&&oe.c(),Te=g(),le&&le.c(),ge=g(),ae&&ae.c(),Fe=g(),a(o,"id","claim-"+t[27]+"-title"),a(o,"class","text-lg font-semibold text-gray-900"),a(p,"class",_="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border "+t[13](t[25].ClaimStatus)),a(p,"aria-label",C="สถานะเคลม: "+rl(t[25].ClaimStatus)),a(m,"class","flex flex-wrap gap-2 mt-2 sm:mt-0"),a(i,"class","flex flex-col sm:flex-row sm:items-center sm:gap-3 mb-2"),a(x,"class","text-gray-700 leading-relaxed"),a(S,"class","mb-3"),a(n,"class","flex-1 mb-4 lg:mb-0"),a(O,"class","text-sm text-gray-500 mb-1"),a(D,"class","text-xl font-bold text-green-600"),a(X,"class","text-sm text-gray-500 mb-1"),a(J,"class","text-lg font-semibold text-gray-900"),a(N,"class","bg-gray-50 rounded-lg p-4 space-y-2"),a(z,"class","lg:text-right lg:ml-6"),a(l,"class","flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4"),a($,"class","grid gap-4 sm:grid-cols-2 lg:grid-cols-3 text-sm"),a(e,"class","border border-gray-200 rounded-lg p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200"),a(e,"aria-labelledby","claim-"+t[27]+"-title")},m(B,ee){k(B,e,ee),r(e,l),r(l,n),r(n,i),r(i,o),r(o,s),r(o,u),r(i,f),r(i,m),r(m,p),r(p,v),r(m,h),fe&&fe.m(m,null),r(n,w),r(n,S),r(S,x),r(x,R),r(l,H),r(l,z),r(z,N),r(N,A),r(A,O),r(A,P),r(A,D),r(D,M),r(N,F),r(N,Y),r(Y,X),r(Y,K),r(Y,J),r(J,ne),r(N,de),re&&re.m(N,null),r(e,ie),r(e,$),se&&se.m($,null),r($,L),me&&me.m($,null),r($,U),oe&&oe.m($,null),r($,Te),le&&le.m($,null),r($,ge),ae&&ae.m($,null),r(e,Fe)},p(B,ee){ee[0]&4&&c!==(c=(B[25].ClaimNo||B[25].ClaimID||"ไม่ระบุ")+"")&&T(u,c),ee[0]&4&&b!==(b=rl(B[25].ClaimStatus)+"")&&T(v,b),ee[0]&4&&_!==(_="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border "+B[13](B[25].ClaimStatus))&&a(p,"class",_),ee[0]&4&&C!==(C="สถานะเคลม: "+rl(B[25].ClaimStatus))&&a(p,"aria-label",C),B[25].ClaimType||B[25].ServiceType?fe?fe.p(B,ee):(fe=bi(B),fe.c(),fe.m(m,null)):fe&&(fe.d(1),fe=null),ee[0]&4&&V!==(V=(B[25].DiagTH||B[25].DiagEN||B[25].Description||"ไม่ระบุรายละเอียด")+"")&&T(R,V),ee[0]&4&&I!==(I=Be(parseInt(B[25].PayableAmt)||0)+"")&&T(M,I),ee[0]&4&&W!==(W=Be(parseInt(B[25].IncurredAmt)||0)+"")&&T(ne,W),ee[0]&4&&(Q=B[25].PayableAmt&&B[25].IncurredAmt&&parseInt(B[25].PayableAmt)!==parseInt(B[25].IncurredAmt)),Q?re?re.p(B,ee):(re=hi(B),re.c(),re.m(N,null)):re&&(re.d(1),re=null),B[25].VisitDate||B[25].ServiceDate?se?se.p(B,ee):(se=yi(B),se.c(),se.m($,L)):se&&(se.d(1),se=null),B[25].ClaimDate?me?me.p(B,ee):(me=_i(B),me.c(),me.m($,U)):me&&(me.d(1),me=null),B[25].SettlementDate?oe?oe.p(B,ee):(oe=vi(B),oe.c(),oe.m($,Te)):oe&&(oe.d(1),oe=null),B[25].ProviderTH||B[25].ProviderEN||B[25].ProviderName?le?le.p(B,ee):(le=wi(B),le.c(),le.m($,ge)):le&&(le.d(1),le=null),B[25].PolicyNo?ae?ae.p(B,ee):(ae=Ci(B),ae.c(),ae.m($,null)):ae&&(ae.d(1),ae=null)},d(B){B&&E(e),fe&&fe.d(),re&&re.d(),se&&se.d(),me&&me.d(),oe&&oe.d(),le&&le.d(),ae&&ae.d()}}}function Qs(t){let e,l,n,i;const o=[Js,Zs,Gs,Ys,qs],s=[];function c(u,f){return u[0]?u[10]?1:u[9]?2:u[7]?4:3:0}return e=c(t),l=s[e]=o[e](t),{c(){l.c(),n=pl()},m(u,f){s[e].m(u,f),k(u,n,f),i=!0},p(u,f){let m=e;e=c(u),e===m?s[e].p(u,f):(Rl(),Ue(s[m],1,1,()=>{s[m]=null}),Ol(),l=s[e],l?l.p(u,f):(l=s[e]=o[e](u),l.c()),He(l,1),l.m(n.parentNode,n))},i(u){i||(He(l),i=!0)},o(u){Ue(l),i=!1},d(u){u&&E(n),s[e].d(u)}}}function Be(t){return t?new Intl.NumberFormat("th-TH",{style:"currency",currency:"THB",minimumFractionDigits:0,maximumFractionDigits:0}).format(t):"ไม่ระบุ"}function tt(t){if(!t)return"ไม่ระบุ";const e=new Date(t),l=e.getDate().toString().padStart(2,"0"),n=e.toLocaleDateString("th-TH",{month:"long"}),i=e.getFullYear()+543;return`${l} ${n} ${i}`}function Gt(t){if(!t)return"ไม่ระบุ";const e=new Date(t),l=e.getDate().toString().padStart(2,"0"),n=e.toLocaleDateString("th-TH",{month:"short"}),i=e.getFullYear()+543;return`${l} ${n} ${i}`}function rl(t){return{Paid:"จ่ายแล้ว",Approved:"อนุมัติแล้ว",Authorized:"อนุญาตแล้ว",Pending:"รอดำเนินการ","Pending For Approval":"รอการอนุมัติ",Open:"เปิดอยู่",Rejected:"ปฏิเสธ"}[t]||t}function sl(t){return{AUTO:"รถยนต์",HEALTH:"สุขภาพ",LIFE:"ชีวิต",PROPERTY:"ทรัพย์สิน",TRAVEL:"การเดินทาง",Repair:"ซ่อมแซม",Medical:"การรักษา",Accident:"อุบัติเหตุ"}[t]||t||"ไม่ระบุ"}function Xs(t,e,l){let n,i,o,s,c,u,f,m,p,b,v,_,C,h;ot(t,$i,D=>l(17,_=D)),ot(t,xe,D=>l(18,C=D)),ot(t,$t,D=>l(19,h=D));const w=Al();let S=null;function x(){w("navigate",{page:"policy-list"})}const V={Active:"bg-green-100 text-green-800 border-green-200",Inactive:"bg-gray-100 text-gray-800 border-gray-200",Expired:"bg-red-100 text-red-800 border-red-200",Pending:"bg-yellow-100 text-yellow-800 border-yellow-200",Cancelled:"bg-gray-100 text-gray-800 border-gray-200"},R={Paid:"bg-green-100 text-green-800 border-green-200",Approved:"bg-blue-100 text-blue-800 border-blue-200",Authorized:"bg-blue-100 text-blue-800 border-blue-200",Pending:"bg-yellow-100 text-yellow-800 border-yellow-200","Pending For Approval":"bg-yellow-100 text-yellow-800 border-yellow-200",Open:"bg-orange-100 text-orange-800 border-orange-200",Rejected:"bg-red-100 text-red-800 border-red-200"},H={AUTO:"bg-purple-100 text-purple-800 border-purple-200",HEALTH:"bg-teal-100 text-teal-800 border-teal-200",LIFE:"bg-indigo-100 text-indigo-800 border-indigo-200",PROPERTY:"bg-amber-100 text-amber-800 border-amber-200",TRAVEL:"bg-cyan-100 text-cyan-800 border-cyan-200",DEFAULT:"bg-gray-100 text-gray-800 border-gray-200"};function z(D){return R[D]||R.Pending}function N(D){const I=D==null?void 0:D.toUpperCase();return H[I]||H.DEFAULT}async function A(){if(s!=null&&s.memberCode)try{await os(s.memberCode),console.log("Policy detail loaded successfully for member:",s.memberCode)}catch(D){console.error("Failed to load policy detail:",D)}}async function O(){if(!(!(s!=null&&s.insurerCode)||!(s!=null&&s.citizenID)))try{const D=s.citizenID,I=await at.policies.searchByCitizenId(D,s.insurerCode);if(I.success&&I.data&&I.data.length>0){const M=I.data[0];l(1,S=M),console.log("Policy list data loaded successfully for member:",s.memberCode,M)}else console.warn("No policy data found for member:",s.memberCode),l(1,S=null)}catch(D){console.error("Failed to load policy list data:",D),l(1,S=null)}}async function P(){await A(),await O()}return t.$$.update=()=>{t.$$.dirty[0]&524288&&l(16,n=h.data),t.$$.dirty[0]&524288&&l(10,i=h.loading),t.$$.dirty[0]&524288&&l(9,o=h.error),t.$$.dirty[0]&262144&&l(0,s=C),t.$$.dirty[0]&131072&&l(8,c=_),t.$$.dirty[0]&1&&s!=null&&s.memberCode&&A(),t.$$.dirty[0]&1&&s!=null&&s.insurerCode&&s!=null&&s.citizenID&&O(),t.$$.dirty[0]&65536&&l(7,u=n),t.$$.dirty[0]&65536&&l(6,f=(n==null?void 0:n.ListPolicyDetail)||[]),t.$$.dirty[0]&65536&&l(5,m=(n==null?void 0:n.BenefitList)||[]),t.$$.dirty[0]&65536&&l(4,p=(n==null?void 0:n.ListContractCondition)||[]),t.$$.dirty[0]&65536&&l(3,b=(n==null?void 0:n.ListMemberCondition)||[]),t.$$.dirty[0]&65536&&l(2,v=(n==null?void 0:n.ListClaimHistory)||[])},[s,S,v,b,p,m,f,u,c,o,i,x,V,z,N,P,n,_,C,h]}class ea extends Jt{constructor(e){super(),Zt(this,e,Xs,Qs,Ht,{},null,[-1,-1])}}function ta(t){let e,l;return e=new Ki({}),e.$on("navigate",t[3]),{c(){kt(e.$$.fragment)},m(n,i){mt(e,n,i),l=!0},p:q,i(n){l||(He(e.$$.fragment,n),l=!0)},o(n){Ue(e.$$.fragment,n),l=!1},d(n){pt(e,n)}}}function la(t){let e,l;return e=new ea({props:{selectedPolicyId:t[1],selectedMemberCode:t[2]}}),e.$on("navigate",t[3]),{c(){kt(e.$$.fragment)},m(n,i){mt(e,n,i),l=!0},p(n,i){const o={};i&2&&(o.selectedPolicyId=n[1]),i&4&&(o.selectedMemberCode=n[2]),e.$set(o)},i(n){l||(He(e.$$.fragment,n),l=!0)},o(n){Ue(e.$$.fragment,n),l=!1},d(n){pt(e,n)}}}function na(t){let e,l;return e=new Ki({}),e.$on("navigate",t[3]),{c(){kt(e.$$.fragment)},m(n,i){mt(e,n,i),l=!0},p:q,i(n){l||(He(e.$$.fragment,n),l=!0)},o(n){Ue(e.$$.fragment,n),l=!1},d(n){pt(e,n)}}}function ia(t){let e,l,n,i,o;const s=[na,la,ta],c=[];function u(f,m){return f[0]==="policy-list"?0:f[0]==="policy-detail"?1:2}return n=u(t),i=c[n]=s[n](t),{c(){e=d("div"),l=d("div"),i.c(),a(l,"class","flex-1"),a(e,"class","min-h-screen bg-gray-50")},m(f,m){k(f,e,m),r(e,l),c[n].m(l,null),o=!0},p(f,[m]){let p=n;n=u(f),n===p?c[n].p(f,m):(Rl(),Ue(c[p],1,1,()=>{c[p]=null}),Ol(),i=c[n],i?i.p(f,m):(i=c[n]=s[n](f),i.c()),He(i,1),i.m(l,null))},i(f){o||(He(i),o=!0)},o(f){Ue(i),o=!1},d(f){f&&E(e),c[n].d()}}}function ra(t,e,l){const n={name:"Insurance Portal",version:"1.0.0",description:"Comprehensive insurance management platform built with Svelte and Tailwind CSS"};let i="policy-list",o=null,s=null;function c(f){l(0,i=f.detail.page),l(1,o=f.detail.policyId||null),l(2,s=f.detail.memberCode||null),console.log("Navigating to:",i,o?`with policy ID: ${o}`:"",s?`with member code: ${s}`:"")}const u={"policy-list":"กรมธรรม์ประกันภัย","policy-detail":"กรมธรรม์ประกันภัย"};return ul(async()=>{try{await is(),console.log("Member store initialized successfully")}catch(f){console.error("Failed to initialize member store:",f)}}),console.log("App initialized:",n),t.$$.update=()=>{t.$$.dirty&1&&typeof document<"u"&&(document.title=u[i]||"Insurance Portal")},[i,o,s,c]}class sa extends Jt{constructor(e){super(),Zt(this,e,ra,ia,Ht,{})}}new sa({target:document.getElementById("app")});
