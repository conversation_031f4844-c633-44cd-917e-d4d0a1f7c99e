{"version": 3, "file": "pure.d.ts", "sourceRoot": "", "sources": ["../src/pure.js"], "names": [], "mappings": ";;;mCAiBwD,CAAC,SAA5C,OAAQ,sBAAsB,EAAE,SAAU,IAC1C,OAAO,sBAAsB,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,sBAAsB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;;;;0BAMvD,CAAC,SAA3C,OAAQ,sBAAsB,EAAE,OAAQ,6DACxC;IACR,WAAW,CAAC,EAAE,WAAW,CAAA;IACzB,OAAO,CAAC,EAAE,CAAC,CAAA;CACZ;;;;yBAMoD,CAAC,SAA5C,OAAQ,sBAAsB,EAAE,SAAU,EACA,CAAC,SAA3C,OAAQ,sBAAsB,EAAE,OAAQ,6DAExC;IACR,SAAS,EAAE,WAAW,CAAA;IACtB,WAAW,EAAE,WAAW,CAAA;IACxB,SAAS,EAAE,OAAO,sBAAsB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;IACpD,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,GAAG,gBAAgB,KAAK,IAAI,CAAA;IACpD,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,sBAAsB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA;IACpF,OAAO,EAAE,MAAM,IAAI,CAAA;CACpB,GAAG,GACD,CAAC,IAAI,MAAM,CAAC,GAAG,OAAO,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACnE;2BAsIS,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,sBAAsB,EAAE,YAAY,CAAC,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,sBAAsB,EAAE,YAAY,CAAC,CAAC;yBAItI,GACP,CAAC,IAAI,OAAO,sBAAsB,EAAE,SAAS,GAAG,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,sBAAsB,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,sBAAsB,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1L;AAvBJ;;;;;;GAMG;AACH,oBAJa,CAAC,OACH,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,IAAG,MAAM,CAAC,CAAA,GAC1B,OAAO,CAAC,CAAC,CAAC,CAStB;AAzBD,+FAA+F;AAC/F,gCAQC;AAkBD;;GAEG;AAEH;;;;GAIG;AAEH;;;;;;;GAOG;AACH,wBAFU,YAAY,GAAG,UAAU,CAEmC;AAvLtE;;;;;GAKG;AAEH;;;;;;;;GAQG;AAEH;;;;;;;;;;;;;;;;GAgBG;AAEH;;;;;;;;;;GAUG;AACH,uBARwD,CAAC,SAA5C,OAAQ,sBAAsB,EAAE,SAAU,EACA,CAAC,SAA3C,OAAQ,sBAAsB,EAAE,OAAQ,sEAE1C,OAAO,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC,YAC/C,sBAAsB,CAAC,CAAC,CAAC,kBACzB,aAAa,CAAC,CAAC,CAAC,GACd,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CA+C9B;AAKD;;;;;;GAMG;AACH,8BAOC"}