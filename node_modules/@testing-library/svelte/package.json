{"name": "@testing-library/svelte", "version": "5.2.8", "description": "Simple and complete Svelte testing utilities that encourage good testing practices.", "main": "src/index.js", "exports": {".": {"types": "./types/index.d.ts", "default": "./src/index.js"}, "./svelte5": {"types": "./types/index.d.ts", "default": "./src/index.js"}, "./vitest": {"types": "./types/vitest.d.ts", "default": "./src/vitest.js"}, "./vite": {"types": "./types/vite.d.ts", "default": "./src/vite.js"}}, "type": "module", "types": "types/index.d.ts", "license": "MIT", "homepage": "https://github.com/testing-library/svelte-testing-library#readme", "repository": {"type": "git", "url": "git+https://github.com/testing-library/svelte-testing-library.git"}, "bugs": {"url": "https://github.com/testing-library/svelte-testing-library/issues"}, "engines": {"node": ">= 10"}, "keywords": ["testing", "svelte", "ui", "dom", "jsdom", "unit", "integration", "functional", "end-to-end", "e2e"], "files": ["src", "types"], "scripts": {"all": "npm-run-all contributors:generate toc format types build test:vitest:* test:jest", "all:legacy": "npm-run-all types:legacy test:vitest:* test:jest", "toc": "doctoc README.md", "lint": "prettier . --check && eslint .", "format": "prettier . --write && eslint . --fix", "setup": "npm run install:5 && npm run all", "test": "vitest run --coverage", "test:watch": "vitest", "test:vitest:jsdom": "vitest run --coverage --environment jsdom", "test:vitest:happy-dom": "vitest run --coverage --environment happy-dom", "test:jest": "npx --node-options=\"--experimental-vm-modules --no-warnings\" jest --coverage", "types": "svelte-check", "types:legacy": "svelte-check --tsconfig tsconfig.legacy.json", "build": "tsc -p tsconfig.build.json && cp src/component-types.d.ts types", "contributors:add": "all-contributors add", "contributors:generate": "all-contributors generate", "preview-release": "./scripts/preview-release", "install:3": "./scripts/install-dependencies 3", "install:4": "./scripts/install-dependencies 4", "install:5": "./scripts/install-dependencies 5"}, "peerDependencies": {"svelte": "^3 || ^4 || ^5 || ^5.0.0-next.0", "vite": "*", "vitest": "*"}, "peerDependenciesMeta": {"vite": {"optional": true}, "vitest": {"optional": true}}, "dependencies": {"@testing-library/dom": "9.x.x || 10.x.x"}, "devDependencies": {"@eslint/js": "^9.26.0", "@jest/globals": "^29.7.0", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@vitest/coverage-v8": "^3.1.3", "@vitest/eslint-plugin": "^1.1.44", "all-contributors-cli": "^6.26.1", "doctoc": "^2.2.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-svelte": "^3.5.1", "eslint-plugin-testing-library": "^7.1.1", "eslint-plugin-unicorn": "^59.0.1", "expect-type": "^1.2.1", "globals": "^16.1.0", "happy-dom": "^17.4.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.28.2", "svelte-check": "^4.1.7", "svelte-jester": "^5.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "typescript-svelte-plugin": "^0.3.46", "vite": "^6.3.5", "vitest": "^3.1.3"}}