{"name": "policy-list-app", "version": "1.0.0", "description": "Insurance Policy List - Responsive Svelte Application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.16", "jsdom": "^23.0.0", "postcss": "^8.4.32", "svelte": "^4.2.7", "tailwindcss": "^3.3.6", "vite": "^5.0.3", "vitest": "^1.0.0"}}