/**
 * CSV Reader Tests
 *
 * Tests for the CSV reading functionality including:
 * - Loading member data from CSV
 * - Loading insurer data from CSV
 * - Error handling for missing files
 * - Data validation and parsing
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { loadMemberData, loadInsurerData, loadCitizenData, clearCSVCache } from '../src/utils/csvReader.js';

// Mock fetch for testing
global.fetch = vi.fn();

describe('CSV Reader', () => {
  beforeEach(() => {
    // Clear cache before each test
    clearCSVCache();
    // Reset fetch mock
    fetch.mockReset();
  });

  describe('loadCitizenData', () => {
    it('should load and parse citizen data from CSV', async () => {
      const mockCSV = `citizenID,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,birthDate,gender,mobile,email,language,citizenship,countryCode
1234567890123,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,1985-03-15,M,0812345678,<EMAIL>,TH,Thai,TH
1234567890124,นาง,มาลี,สวยงาม,Mrs.,<PERSON><PERSON>,Suayngam,1990-07-22,F,0823456789,<EMAIL>,TH,Thai,TH`;

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      const result = await loadCitizenData();

      expect(fetch).toHaveBeenCalledWith('/data/citizens.csv');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        citizenID: '1234567890123',
        titleTH: 'นาย',
        nameTH: 'สมชาย',
        surnameTH: 'ใจดี',
        titleEN: 'Mr.',
        nameEN: 'Somchai',
        surnameEN: 'Jaidee',
        birthDate: '1985-03-15',
        gender: 'M',
        mobile: '0812345678',
        email: '<EMAIL>',
        language: 'TH',
        citizenship: 'Thai',
        countryCode: 'TH'
      });
    });
  });

  describe('loadMemberData', () => {
    it('should load and join member data with citizen data', async () => {
      const mockMemberCSV = `memberCode,citizenID,insurerCode,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,Active,Principal,N,Standard,TH,Thai,TH
MEM002,1234567890124,INS002,Active,Principal,N,Standard,TH,Thai,TH`;

      const mockCitizenCSV = `citizenID,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,birthDate,gender,mobile,email,language,citizenship,countryCode
1234567890123,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,1985-03-15,M,0812345678,<EMAIL>,TH,Thai,TH
1234567890124,นาง,มาลี,สวยงาม,Mrs.,Malee,Suayngam,1990-07-22,F,0823456789,<EMAIL>,TH,Thai,TH`;

      fetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(mockMemberCSV)
        })
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(mockCitizenCSV)
        });

      const result = await loadMemberData();

      expect(fetch).toHaveBeenCalledWith('/data/members.csv');
      expect(fetch).toHaveBeenCalledWith('/data/citizens.csv');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        memberCode: 'MEM001',
        citizenID: '1234567890123',
        insurerCode: 'INS001',
        memberStatus: 'Active',
        memberType: 'Principal',
        vip: 'N',
        cardType: 'Standard',
        language: 'TH',
        citizenship: 'Thai',
        countryCode: 'TH',
        titleTH: 'นาย',
        nameTH: 'สมชาย',
        surnameTH: 'ใจดี',
        titleEN: 'Mr.',
        nameEN: 'Somchai',
        surnameEN: 'Jaidee',
        birthDate: '1985-03-15',
        gender: 'M',
        mobile: '0812345678',
        email: '<EMAIL>'
      });
    });

    it('should handle fetch errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await loadMemberData();

      // Should return fallback data
      expect(result).toHaveLength(1);
      expect(result[0].memberCode).toBe('MEM001');
    });

    it('should handle invalid CSV format', async () => {
      const invalidMemberCSV = 'invalid,csv,format\nwith,wrong,number,of,columns';
      const invalidCitizenCSV = 'invalid,csv,format\nwith,wrong,number,of,columns';

      fetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(invalidMemberCSV)
        })
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(invalidCitizenCSV)
        });

      const result = await loadMemberData();

      // Should return fallback data when CSV is invalid
      expect(result).toHaveLength(1);
    });

    it('should filter out rows with missing required fields', async () => {
      const mockMemberCSV = `memberCode,citizenID,insurerCode,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,Active,Principal,N,Standard,TH,Thai,TH
,1234567890124,INS002,Active,Principal,N,Standard,TH,Thai,TH
MEM003,,INS003,Active,Principal,N,Standard,TH,Thai,TH`;

      const mockCitizenCSV = `citizenID,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,birthDate,gender,mobile,email,language,citizenship,countryCode
1234567890123,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,1985-03-15,M,0812345678,<EMAIL>,TH,Thai,TH
1234567890124,นาง,มาลี,สวยงาม,Mrs.,Malee,Suayngam,1990-07-22,F,0823456789,<EMAIL>,TH,Thai,TH`;

      fetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(mockMemberCSV)
        })
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(mockCitizenCSV)
        });

      const result = await loadMemberData();

      // Should only return the valid row (MEM001)
      expect(result).toHaveLength(1);
      expect(result[0].memberCode).toBe('MEM001');
    });
  });

  describe('loadInsurerData', () => {
    it('should load and parse insurer data from CSV', async () => {
      const mockCSV = `insurerCode,displayNameTH,displayNameEN
INS001,บริษัท ประกันภัย เอ จำกัด,Insurance Company A Ltd.
INS002,บริษัท ประกันชีวิต แอล จำกัด,Life Insurance L Ltd.`;

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      const result = await loadInsurerData();

      expect(fetch).toHaveBeenCalledWith('/data/insurers.csv');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        insurerCode: 'INS001',
        displayNameTH: 'บริษัท ประกันภัย เอ จำกัด',
        displayNameEN: 'Insurance Company A Ltd.'
      });
    });

    it('should handle fetch errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await loadInsurerData();

      // Should return fallback data
      expect(result).toHaveLength(1);
      expect(result[0].insurerCode).toBe('INS001');
    });
  });

  describe('caching', () => {
    it('should cache loaded data', async () => {
      const mockMemberCSV = `memberCode,citizenID,insurerCode,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,Active,Principal,N,Standard,TH,Thai,TH`;

      const mockCitizenCSV = `citizenID,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,birthDate,gender,mobile,email,language,citizenship,countryCode
1234567890123,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,1985-03-15,M,0812345678,<EMAIL>,TH,Thai,TH`;

      fetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(mockMemberCSV)
        })
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(mockCitizenCSV)
        });

      // First call
      await loadMemberData();

      // Second call should use cache
      await loadMemberData();

      // Fetch should only be called twice (once for each CSV file)
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should reload data when forceReload is true', async () => {
      const mockMemberCSV = `memberCode,citizenID,insurerCode,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,Active,Principal,N,Standard,TH,Thai,TH`;

      const mockCitizenCSV = `citizenID,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,birthDate,gender,mobile,email,language,citizenship,countryCode
1234567890123,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,1985-03-15,M,0812345678,<EMAIL>,TH,Thai,TH`;

      fetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve(mockMemberCSV)
      });

      fetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve(mockCitizenCSV)
      });

      // First call
      await loadMemberData();

      // Second call with forceReload
      await loadMemberData(true);

      // Fetch should be called four times (twice for each CSV file)
      expect(fetch).toHaveBeenCalledTimes(4);
    });
  });
});
